version: '3.8'

services:
  # PostgreSQL database with PostGIS for testing
  test-db:
    image: postgis/postgis:17-3.5
    environment:
      POSTGRES_DB: izymercado_test
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"  # Map to standard PostgreSQL port for local access
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    volumes:
      - test_db_data:/var/lib/postgresql/data

  # Backend API service built from local ../backend directory
  test-api:
    build:
      context: ../backend
      dockerfile: Dockerfile
    env_file:
      - ../backend/.env
    environment:
      # Override specific values for testing
      DATABASE_URL: *****************************************/izymercado_test
      PORT: 8080
      APP_ENV: test
    ports:
      - "8081:8080"
    depends_on:
      test-db:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 5s
      timeout: 10s
      retries: 24  # 24 * 5s = 2 minutes total wait time
      start_period: 60s  # Give 60s before starting health checks
    restart: unless-stopped
    volumes:
      # Mount source code for development (optional - remove if not needed)
      - ../backend:/app/src:ro

  # Mock API service (fallback when backend is not available)
  mock-api:
    image: node:18-alpine
    working_dir: /app
    volumes:
      - ./e2e/mock-api:/app
    command: sh -c "npm install && npm start"
    environment:
      PORT: 8080
    ports:
      - "8082:8080"
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 15s

volumes:
  test_db_data:

#!/bin/bash

# Script to help view E2E test videos
# Usage: ./scripts/view-test-videos.sh

echo "📹 E2E Test Videos Viewer"
echo "========================="

# Check if test-results directory exists
if [ ! -d "test-results" ]; then
    echo "❌ No test-results directory found."
    echo "💡 Run tests first with: npm run test:e2e"
    exit 1
fi

# Find all video files
videos=$(find test-results -name "*.webm" -o -name "*.mp4" 2>/dev/null)

if [ -z "$videos" ]; then
    echo "❌ No video files found in test-results/"
    echo "💡 Make sure you've run tests with video recording enabled"
    exit 1
fi

echo "📁 Found test videos:"
echo ""

# List videos with details
count=1
declare -a video_array
while IFS= read -r video; do
    if [ -n "$video" ]; then
        video_array[$count]="$video"
        size=$(du -h "$video" | cut -f1)
        modified=$(stat -c %y "$video" 2>/dev/null || stat -f %Sm "$video" 2>/dev/null)
        echo "$count) $(basename "$video") ($size) - $modified"
        ((count++))
    fi
done <<< "$videos"

echo ""
echo "🎬 Options:"
echo "1. Open specific video (enter number)"
echo "2. Open all videos"
echo "3. Open test-results folder"
echo "4. Exit"
echo ""

read -p "Choose an option (1-4): " choice

case $choice in
    1)
        read -p "Enter video number: " video_num
        if [ -n "${video_array[$video_num]}" ]; then
            echo "🎬 Opening: ${video_array[$video_num]}"
            # Try different video players based on OS
            if command -v xdg-open > /dev/null; then
                xdg-open "${video_array[$video_num]}"
            elif command -v open > /dev/null; then
                open "${video_array[$video_num]}"
            elif command -v vlc > /dev/null; then
                vlc "${video_array[$video_num]}"
            else
                echo "📁 Video location: ${video_array[$video_num]}"
                echo "💡 Open this file with your preferred video player"
            fi
        else
            echo "❌ Invalid video number"
        fi
        ;;
    2)
        echo "🎬 Opening all videos..."
        for video in "${video_array[@]}"; do
            if [ -n "$video" ]; then
                if command -v xdg-open > /dev/null; then
                    xdg-open "$video"
                elif command -v open > /dev/null; then
                    open "$video"
                elif command -v vlc > /dev/null; then
                    vlc "$video"
                fi
            fi
        done
        ;;
    3)
        echo "📁 Test results folder location:"
        echo "$(pwd)/test-results/"
        echo ""
        echo "📹 Available videos:"
        for video in "${video_array[@]}"; do
            if [ -n "$video" ]; then
                echo "  $video"
            fi
        done
        ;;
    4)
        echo "👋 Goodbye!"
        exit 0
        ;;
    *)
        echo "❌ Invalid option"
        exit 1
        ;;
esac

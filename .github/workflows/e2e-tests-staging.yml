name: E2E Tests - Staging Environment

on:
  pull_request:
    branches: [ staging ]
    types: [ opened, synchronize, reopened ]
  workflow_dispatch:
    inputs:
      backend_branch:
        description: 'Backend branch to test against'
        required: false
        default: 'main'
        type: string

jobs:
  e2e-tests-staging:
    name: Run E2E Tests (Staging)
    runs-on: ubuntu-latest
    timeout-minutes: 30

    env:
      CI: true
      BACKEND_REPO_URL: https://github.com/izy-mercado/backend.git
      BACKEND_BRANCH: ${{ github.event.inputs.backend_branch || 'main' }}
      PLAYWRIGHT_BASE_URL: http://localhost:4173

      # Backend Environment Variables from GitHub Secrets
      JWT_SECRET: ${{ secrets.JWT_SECRET }}
      BASIC_AUTH_PASSWORD: ${{ secrets.BASIC_AUTH_PASSWORD }}
      CLOUDFLARE_R2_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_R2_ACCOUNT_ID }}
      CLOUDFLARE_R2_ACCESS_KEY_ID: ${{ secrets.CLOUDFLARE_R2_ACCESS_KEY_ID }}
      CLOUDFLARE_R2_SECRET_ACCESS_KEY: ${{ secrets.CLOUDFLARE_R2_SECRET_ACCESS_KEY }}
      CLOUDFLARE_R2_BUCKET_NAME: ${{ secrets.CLOUDFLARE_R2_BUCKET_NAME }}
      CLOUDFLARE_R2_PUBLIC_URL: ${{ secrets.CLOUDFLARE_R2_PUBLIC_URL }}
      MAPBOX_ACCESS_TOKEN: ${{ secrets.MAPBOX_ACCESS_TOKEN }}
      VIACEP_API_URL: ${{ secrets.VIACEP_API_URL }}
      FIREBASE_PROJECT_ID: ${{ secrets.FIREBASE_PROJECT_ID }}
      FIREBASE_PRIVATE_KEY_ID: ${{ secrets.FIREBASE_PRIVATE_KEY_ID }}
      FIREBASE_PRIVATE_KEY: ${{ secrets.FIREBASE_PRIVATE_KEY }}
      FIREBASE_CLIENT_EMAIL: ${{ secrets.FIREBASE_CLIENT_EMAIL }}
      FIREBASE_CLIENT_ID: ${{ secrets.FIREBASE_CLIENT_ID }}
      FIREBASE_AUTH_URI: ${{ secrets.FIREBASE_AUTH_URI }}
      FIREBASE_TOKEN_URI: ${{ secrets.FIREBASE_TOKEN_URI }}
      FIREBASE_AUTH_PROVIDER_X509_CERT_URL: ${{ secrets.FIREBASE_AUTH_PROVIDER_X509_CERT_URL }}
      FIREBASE_CLIENT_X509_CERT_URL: ${{ secrets.FIREBASE_CLIENT_X509_CERT_URL }}
      SMTP_HOST: ${{ secrets.SMTP_HOST }}
      SMTP_PORT: ${{ secrets.SMTP_PORT }}
      SMTP_USERNAME: ${{ secrets.SMTP_USERNAME }}
      SMTP_PASSWORD: ${{ secrets.SMTP_PASSWORD }}
      SMTP_FROM_EMAIL: ${{ secrets.SMTP_FROM_EMAIL }}
      SMTP_FROM_NAME: ${{ secrets.SMTP_FROM_NAME }}
      STRIPE_SECRET_KEY: ${{ secrets.STRIPE_SECRET_KEY }}
      STRIPE_WEBHOOK_SECRET: ${{ secrets.STRIPE_WEBHOOK_SECRET }}
      WHATSAPP_API_URL: ${{ secrets.WHATSAPP_API_URL }}
      WHATSAPP_API_TOKEN: ${{ secrets.WHATSAPP_API_TOKEN }}

    steps:
      - name: Checkout frontend repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Configure Docker daemon
        run: |
          sudo systemctl start docker
          sudo chmod 666 /var/run/docker.sock
          docker --version
          docker-compose --version

      - name: Install frontend dependencies
        run: npm ci

      - name: Install Playwright browsers
        run: npx playwright install --with-deps chromium firefox webkit

      - name: Configure E2E test environment
        run: |
          cp .env.test .env
          echo "CI=true" >> .env
          echo "BACKEND_REPO_URL=${{ env.BACKEND_REPO_URL }}" >> .env
          echo "BACKEND_BRANCH=${{ env.BACKEND_BRANCH }}" >> .env
          echo "PLAYWRIGHT_BASE_URL=${{ env.PLAYWRIGHT_BASE_URL }}" >> .env
          echo "Environment configuration:"
          cat .env

      - name: Pre-pull Docker images
        run: |
          docker pull postgis/postgis:17-3.5
          docker pull golang:1.23-alpine
          docker pull alpine:latest

      - name: Run E2E tests with backend services
        run: npm run test:e2e
        timeout-minutes: 20

      - name: Upload Playwright HTML report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: playwright-report-staging-${{ github.run_number }}
          path: |
            playwright-report/
            test-results/
          retention-days: 30

      - name: Upload test videos and screenshots
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: test-failures-staging-${{ github.run_number }}
          path: |
            test-results/**/*.webm
            test-results/**/*.png
          retention-days: 7

      - name: Generate test summary
        if: always()
        run: |
          echo "## 🧪 E2E Test Results - Staging Environment" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Environment Details:**" >> $GITHUB_STEP_SUMMARY
          echo "- Backend Repository: \`${{ env.BACKEND_REPO_URL }}\`" >> $GITHUB_STEP_SUMMARY
          echo "- Backend Branch: \`${{ env.BACKEND_BRANCH }}\`" >> $GITHUB_STEP_SUMMARY
          echo "- Frontend URL: \`${{ env.PLAYWRIGHT_BASE_URL }}\`" >> $GITHUB_STEP_SUMMARY
          echo "- Docker Compose: \`docker-compose.ci.yml\`" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          if [ -f "test-results/results.json" ]; then
            echo "**Test Results:**" >> $GITHUB_STEP_SUMMARY
            node -e "
              const fs = require('fs');
              try {
                const results = JSON.parse(fs.readFileSync('test-results/results.json', 'utf8'));
                const stats = results.stats || {};
                console.log(\`- ✅ **Passed**: \${stats.passed || 0}\`);
                console.log(\`- ❌ **Failed**: \${stats.failed || 0}\`);
                console.log(\`- ⏭️ **Skipped**: \${stats.skipped || 0}\`);
                console.log(\`- ⏱️ **Duration**: \${Math.round((stats.duration || 0) / 1000)}s\`);
              } catch (error) {
                console.log('- ⚠️ Test results parsing failed');
              }
            " >> $GITHUB_STEP_SUMMARY
          else
            echo "- ⚠️ Test results file not found" >> $GITHUB_STEP_SUMMARY
          fi

      - name: Comment PR with test results
        uses: actions/github-script@v7
        if: github.event_name == 'pull_request' && always()
        with:
          script: |
            const fs = require('fs');

            let comment = '## 🧪 E2E Test Results - Staging Environment\n\n';
            comment += `**Environment Configuration:**\n`;
            comment += `- 🌐 Backend: \`${{ env.BACKEND_REPO_URL }}\` (branch: \`${{ env.BACKEND_BRANCH }}\`)\n`;
            comment += `- 🎭 Frontend: \`${{ env.PLAYWRIGHT_BASE_URL }}\`\n`;
            comment += `- 🐳 Docker Compose: \`docker-compose.ci.yml\`\n\n`;

            try {
              if (fs.existsSync('test-results/results.json')) {
                const results = JSON.parse(fs.readFileSync('test-results/results.json', 'utf8'));
                const stats = results.stats || {};

                comment += `**Test Results:**\n`;
                comment += `- ✅ **Passed**: ${stats.passed || 0}\n`;
                comment += `- ❌ **Failed**: ${stats.failed || 0}\n`;
                comment += `- ⏭️ **Skipped**: ${stats.skipped || 0}\n`;
                comment += `- ⏱️ **Duration**: ${Math.round((stats.duration || 0) / 1000)}s\n\n`;

                if (stats.failed > 0) {
                  comment += '❌ Some tests failed. Check the [workflow run](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}) for details.\n\n';
                  comment += '📊 [View detailed test report](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}) with screenshots and videos.\n';
                } else {
                  comment += '✅ All E2E tests passed successfully!\n';
                }
              } else {
                comment += '⚠️ Test results not available. Check the workflow logs for details.\n';
              }
            } catch (error) {
              comment += `⚠️ Error reading test results: ${error.message}\n`;
            }

            comment += '\n---\n';
            comment += '*🤖 This comment was automatically generated by the E2E testing workflow.*';

            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });

      - name: Cleanup Docker resources
        if: always()
        run: |
          echo "Cleaning up Docker resources..."
          docker-compose -f docker-compose.ci.yml down -v --remove-orphans || true
          docker system prune -f || true

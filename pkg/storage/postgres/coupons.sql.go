// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: coupons.sql

package postgres

import (
	"context"
	"database/sql"
	"time"
)

const createCoupon = `-- name: CreateCoupon :one
INSERT INTO coupons (
    code,
    type,
    value,
    quantity,
    original_quantity,
    is_active,
    expires_at,
    min_order_value,
    owner_type,
    owner_id,
    external_id
) VALUES (
    $1, $2, $3, $4, $4, $5, $6, $7, $8, $9, $10
) RETURNING external_id
`

type CreateCouponParams struct {
	Code          string
	Type          string
	Value         int32
	Quantity      int32
	IsActive      bool
	ExpiresAt     time.Time
	MinOrderValue int32
	OwnerType     string
	OwnerID       int32
	ExternalID    string
}

func (q *Queries) CreateCoupon(ctx context.Context, arg CreateCouponParams) (string, error) {
	row := q.db.QueryRow(ctx, createCoupon,
		arg.Code,
		arg.Type,
		arg.Value,
		arg.Quantity,
		arg.IsActive,
		arg.ExpiresAt,
		arg.MinOrderValue,
		arg.OwnerType,
		arg.OwnerID,
		arg.ExternalID,
	)
	var external_id string
	err := row.Scan(&external_id)
	return external_id, err
}

const getActiveCouponByCode = `-- name: GetActiveCouponByCode :one
SELECT id, code, type, value, quantity, is_active, expires_at, min_order_value, owner_type, owner_id, external_id, created_at, updated_at, original_quantity FROM coupons WHERE code = $1 AND is_active = true
`

func (q *Queries) GetActiveCouponByCode(ctx context.Context, code string) (Coupon, error) {
	row := q.db.QueryRow(ctx, getActiveCouponByCode, code)
	var i Coupon
	err := row.Scan(
		&i.ID,
		&i.Code,
		&i.Type,
		&i.Value,
		&i.Quantity,
		&i.IsActive,
		&i.ExpiresAt,
		&i.MinOrderValue,
		&i.OwnerType,
		&i.OwnerID,
		&i.ExternalID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.OriginalQuantity,
	)
	return i, err
}

const getCouponByCode = `-- name: GetCouponByCode :one
SELECT id, code, type, value, quantity, is_active, expires_at, min_order_value, owner_type, owner_id, external_id, created_at, updated_at, original_quantity FROM coupons WHERE code = $1
`

func (q *Queries) GetCouponByCode(ctx context.Context, code string) (Coupon, error) {
	row := q.db.QueryRow(ctx, getCouponByCode, code)
	var i Coupon
	err := row.Scan(
		&i.ID,
		&i.Code,
		&i.Type,
		&i.Value,
		&i.Quantity,
		&i.IsActive,
		&i.ExpiresAt,
		&i.MinOrderValue,
		&i.OwnerType,
		&i.OwnerID,
		&i.ExternalID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.OriginalQuantity,
	)
	return i, err
}

const getCouponByExternalID = `-- name: GetCouponByExternalID :one
SELECT
    c.id,
    c.code,
    c.type,
    c.value,
    c.quantity,
    c.original_quantity,
    c.is_active,
    c.expires_at,
    c.min_order_value,
    c.owner_type,
    c.owner_id,
    c.external_id,
    c.created_at,
    c.updated_at,
    COALESCE(u.external_id, co.external_id) AS owner_external_id,
    co.name AS owner_name,
    co.cnpj AS owner_cnpj
FROM coupons c
LEFT JOIN users u
    ON c.owner_type = 'admin' AND c.owner_id = u.id
LEFT JOIN companies co
    ON c.owner_type = 'company' AND c.owner_id = co.id
WHERE c.external_id = $1
`

type GetCouponByExternalIDRow struct {
	ID               int32
	Code             string
	Type             string
	Value            int32
	Quantity         int32
	OriginalQuantity int32
	IsActive         bool
	ExpiresAt        time.Time
	MinOrderValue    int32
	OwnerType        string
	OwnerID          int32
	ExternalID       string
	CreatedAt        sql.NullTime
	UpdatedAt        sql.NullTime
	OwnerExternalID  string
	OwnerName        sql.NullString
	OwnerCnpj        sql.NullString
}

func (q *Queries) GetCouponByExternalID(ctx context.Context, externalID string) (GetCouponByExternalIDRow, error) {
	row := q.db.QueryRow(ctx, getCouponByExternalID, externalID)
	var i GetCouponByExternalIDRow
	err := row.Scan(
		&i.ID,
		&i.Code,
		&i.Type,
		&i.Value,
		&i.Quantity,
		&i.OriginalQuantity,
		&i.IsActive,
		&i.ExpiresAt,
		&i.MinOrderValue,
		&i.OwnerType,
		&i.OwnerID,
		&i.ExternalID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.OwnerExternalID,
		&i.OwnerName,
		&i.OwnerCnpj,
	)
	return i, err
}

const hasUserUsedCoupon = `-- name: HasUserUsedCoupon :one
SELECT COUNT(1) FROM users_coupons WHERE user_id = $1 AND coupon_id = $2
`

type HasUserUsedCouponParams struct {
	UserID   int32
	CouponID int32
}

func (q *Queries) HasUserUsedCoupon(ctx context.Context, arg HasUserUsedCouponParams) (int64, error) {
	row := q.db.QueryRow(ctx, hasUserUsedCoupon, arg.UserID, arg.CouponID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const listCoupons = `-- name: ListCoupons :many
SELECT
    c.code,
    c.type,
    c.value,
    c.quantity,
    c.original_quantity,
    c.is_active,
    c.expires_at,
    c.min_order_value,
    c.owner_type,
    c.external_id,
    c.created_at,
    c.updated_at,
    COALESCE(u.external_id, co.external_id) AS owner_external_id,
    co.name AS owner_name,
    co.cnpj AS owner_cnpj,
    COUNT(*) OVER() AS total_count
FROM coupons c
LEFT JOIN users u
    ON c.owner_type = 'admin' AND c.owner_id = u.id
LEFT JOIN companies co
    ON c.owner_type = 'company' AND c.owner_id = co.id
ORDER BY c.created_at DESC
LIMIT $1 OFFSET $2
`

type ListCouponsParams struct {
	Limit  int32
	Offset int32
}

type ListCouponsRow struct {
	Code             string
	Type             string
	Value            int32
	Quantity         int32
	OriginalQuantity int32
	IsActive         bool
	ExpiresAt        time.Time
	MinOrderValue    int32
	OwnerType        string
	ExternalID       string
	CreatedAt        sql.NullTime
	UpdatedAt        sql.NullTime
	OwnerExternalID  string
	OwnerName        sql.NullString
	OwnerCnpj        sql.NullString
	TotalCount       int64
}

func (q *Queries) ListCoupons(ctx context.Context, arg ListCouponsParams) ([]ListCouponsRow, error) {
	rows, err := q.db.Query(ctx, listCoupons, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ListCouponsRow
	for rows.Next() {
		var i ListCouponsRow
		if err := rows.Scan(
			&i.Code,
			&i.Type,
			&i.Value,
			&i.Quantity,
			&i.OriginalQuantity,
			&i.IsActive,
			&i.ExpiresAt,
			&i.MinOrderValue,
			&i.OwnerType,
			&i.ExternalID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.OwnerExternalID,
			&i.OwnerName,
			&i.OwnerCnpj,
			&i.TotalCount,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const registerCouponUsage = `-- name: RegisterCouponUsage :exec
INSERT INTO users_coupons (user_id, coupon_id) VALUES ($1, $2)
ON CONFLICT (user_id, coupon_id) DO NOTHING
`

type RegisterCouponUsageParams struct {
	UserID   int32
	CouponID int32
}

func (q *Queries) RegisterCouponUsage(ctx context.Context, arg RegisterCouponUsageParams) error {
	_, err := q.db.Exec(ctx, registerCouponUsage, arg.UserID, arg.CouponID)
	return err
}

const updateCouponStatus = `-- name: UpdateCouponStatus :exec
UPDATE coupons 
SET is_active = $2, 
    updated_at = NOW() 
WHERE external_id = $1
`

type UpdateCouponStatusParams struct {
	ExternalID string
	IsActive   bool
}

func (q *Queries) UpdateCouponStatus(ctx context.Context, arg UpdateCouponStatusParams) error {
	_, err := q.db.Exec(ctx, updateCouponStatus, arg.ExternalID, arg.IsActive)
	return err
}

const useCoupon = `-- name: UseCoupon :one
UPDATE coupons SET quantity = quantity - 1 WHERE code = $1 AND quantity > 0 RETURNING quantity
`

func (q *Queries) UseCoupon(ctx context.Context, code string) (int32, error) {
	row := q.db.QueryRow(ctx, useCoupon, code)
	var quantity int32
	err := row.Scan(&quantity)
	return quantity, err
}

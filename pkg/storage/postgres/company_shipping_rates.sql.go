// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: company_shipping_rates.sql

package postgres

import (
	"context"
	"database/sql"
	"time"

	"github.com/jackc/pgtype"
)

const calculateShippingFeeWithCoordinates = `-- name: CalculateShippingFeeWithCoordinates :one
WITH company_location AS (
    SELECT
        c.id as company_id,
        ca.location
    FROM companies c
    JOIN company_addresses ca ON c.id = ca.company_id
    WHERE $1 = c.external_id
    LIMIT 1
),
distance_calc AS (
    SELECT
        cl.company_id,
        ROUND(
            (ST_Distance(
                cl.location::geography,
                ST_Point($2, $3)::geography
            ) / 1000.0)::numeric, 2
        ) AS distance_km
    FROM company_location cl
)
SELECT
    dc.distance_km,
    COALESCE(csr.fee_centavos, 0) as shipping_fee_centavos
FROM distance_calc dc
LEFT JOIN company_shipping_rates csr ON csr.company_id = dc.company_id
    AND dc.distance_km >= csr.distance_min_km
    AND dc.distance_km < csr.distance_max_km
LIMIT 1
`

type CalculateShippingFeeWithCoordinatesParams struct {
	CompanyExternalID string
	UserLongitude     interface{}
	UserLatitude      interface{}
}

type CalculateShippingFeeWithCoordinatesRow struct {
	DistanceKm          pgtype.Numeric
	ShippingFeeCentavos int32
}

func (q *Queries) CalculateShippingFeeWithCoordinates(ctx context.Context, arg CalculateShippingFeeWithCoordinatesParams) (CalculateShippingFeeWithCoordinatesRow, error) {
	row := q.db.QueryRow(ctx, calculateShippingFeeWithCoordinates, arg.CompanyExternalID, arg.UserLongitude, arg.UserLatitude)
	var i CalculateShippingFeeWithCoordinatesRow
	err := row.Scan(&i.DistanceKm, &i.ShippingFeeCentavos)
	return i, err
}

const calculateShippingFeeWithDistance = `-- name: CalculateShippingFeeWithDistance :one
WITH company_location AS (
    SELECT 
        c.id as company_id,
        ca.latitude,
        ca.longitude,
        ca.location
    FROM companies c
    JOIN company_addresses ca ON c.id = ca.company_id AND ca.is_default = true
    WHERE $1 = c.external_id
),
user_location AS (
    SELECT 
        ua.latitude,
        ua.longitude,
        ua.location
    FROM user_addresses ua
    WHERE $2 = ua.external_id
),
distance_calc AS (
    SELECT 
        cl.company_id,
        ROUND(
            (ST_Distance(cl.location::geography, ul.location::geography) / 1000.0)::numeric, 2
        ) AS distance_km
    FROM company_location cl
    CROSS JOIN user_location ul
)
SELECT 
    dc.distance_km,
    COALESCE(csr.fee_centavos, 0) as shipping_fee_centavos
FROM distance_calc dc
LEFT JOIN company_shipping_rates csr ON csr.company_id = dc.company_id
    AND dc.distance_km >= csr.distance_min_km
    AND dc.distance_km < csr.distance_max_km
LIMIT 1
`

type CalculateShippingFeeWithDistanceParams struct {
	CompanyExternalID     string
	UserAddressExternalID string
}

type CalculateShippingFeeWithDistanceRow struct {
	DistanceKm          pgtype.Numeric
	ShippingFeeCentavos int32
}

func (q *Queries) CalculateShippingFeeWithDistance(ctx context.Context, arg CalculateShippingFeeWithDistanceParams) (CalculateShippingFeeWithDistanceRow, error) {
	row := q.db.QueryRow(ctx, calculateShippingFeeWithDistance, arg.CompanyExternalID, arg.UserAddressExternalID)
	var i CalculateShippingFeeWithDistanceRow
	err := row.Scan(&i.DistanceKm, &i.ShippingFeeCentavos)
	return i, err
}

const createCompanyShippingRate = `-- name: CreateCompanyShippingRate :one
INSERT INTO company_shipping_rates (
    company_id, distance_min_km, distance_max_km, fee_centavos, external_id
) VALUES (
    $1, $2, $3, $4, $5
) RETURNING id
`

type CreateCompanyShippingRateParams struct {
	CompanyID     int32
	DistanceMinKm pgtype.Numeric
	DistanceMaxKm pgtype.Numeric
	FeeCentavos   int32
	ExternalID    string
}

func (q *Queries) CreateCompanyShippingRate(ctx context.Context, arg CreateCompanyShippingRateParams) (int32, error) {
	row := q.db.QueryRow(ctx, createCompanyShippingRate,
		arg.CompanyID,
		arg.DistanceMinKm,
		arg.DistanceMaxKm,
		arg.FeeCentavos,
		arg.ExternalID,
	)
	var id int32
	err := row.Scan(&id)
	return id, err
}

const deleteAllCompanyShippingRates = `-- name: DeleteAllCompanyShippingRates :exec
DELETE FROM company_shipping_rates
WHERE company_id = $1
`

func (q *Queries) DeleteAllCompanyShippingRates(ctx context.Context, companyID int32) error {
	_, err := q.db.Exec(ctx, deleteAllCompanyShippingRates, companyID)
	return err
}

const deleteCompanyShippingRate = `-- name: DeleteCompanyShippingRate :exec
DELETE FROM company_shipping_rates
WHERE external_id = $1
`

func (q *Queries) DeleteCompanyShippingRate(ctx context.Context, externalID string) error {
	_, err := q.db.Exec(ctx, deleteCompanyShippingRate, externalID)
	return err
}

const getCompanyShippingRateByExternalID = `-- name: GetCompanyShippingRateByExternalID :one
SELECT 
    id,
    company_id,
    distance_min_km,
    distance_max_km,
    fee_centavos,
    external_id,
    created_at,
    updated_at
FROM company_shipping_rates
WHERE external_id = $1
`

func (q *Queries) GetCompanyShippingRateByExternalID(ctx context.Context, externalID string) (CompanyShippingRate, error) {
	row := q.db.QueryRow(ctx, getCompanyShippingRateByExternalID, externalID)
	var i CompanyShippingRate
	err := row.Scan(
		&i.ID,
		&i.CompanyID,
		&i.DistanceMinKm,
		&i.DistanceMaxKm,
		&i.FeeCentavos,
		&i.ExternalID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getCompanyShippingRates = `-- name: GetCompanyShippingRates :many
SELECT 
    id,
    company_id,
    distance_min_km,
    distance_max_km,
    fee_centavos,
    external_id,
    created_at,
    updated_at
FROM company_shipping_rates
WHERE company_id = $1
ORDER BY distance_min_km ASC
`

func (q *Queries) GetCompanyShippingRates(ctx context.Context, companyID int32) ([]CompanyShippingRate, error) {
	rows, err := q.db.Query(ctx, getCompanyShippingRates, companyID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []CompanyShippingRate
	for rows.Next() {
		var i CompanyShippingRate
		if err := rows.Scan(
			&i.ID,
			&i.CompanyID,
			&i.DistanceMinKm,
			&i.DistanceMaxKm,
			&i.FeeCentavos,
			&i.ExternalID,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getCompanyShippingRatesWithCompanyInfo = `-- name: GetCompanyShippingRatesWithCompanyInfo :many
SELECT 
    csr.id,
    csr.company_id,
    csr.distance_min_km,
    csr.distance_max_km,
    csr.fee_centavos,
    csr.external_id,
    csr.created_at,
    csr.updated_at,
    c.name as company_name,
    c.external_id as company_external_id
FROM company_shipping_rates csr
JOIN companies c ON csr.company_id = c.id
WHERE c.owner_id = $1
ORDER BY c.name ASC, csr.distance_min_km ASC
`

type GetCompanyShippingRatesWithCompanyInfoRow struct {
	ID                int32
	CompanyID         int32
	DistanceMinKm     pgtype.Numeric
	DistanceMaxKm     pgtype.Numeric
	FeeCentavos       int32
	ExternalID        string
	CreatedAt         time.Time
	UpdatedAt         time.Time
	CompanyName       string
	CompanyExternalID string
}

func (q *Queries) GetCompanyShippingRatesWithCompanyInfo(ctx context.Context, ownerID sql.NullInt32) ([]GetCompanyShippingRatesWithCompanyInfoRow, error) {
	rows, err := q.db.Query(ctx, getCompanyShippingRatesWithCompanyInfo, ownerID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetCompanyShippingRatesWithCompanyInfoRow
	for rows.Next() {
		var i GetCompanyShippingRatesWithCompanyInfoRow
		if err := rows.Scan(
			&i.ID,
			&i.CompanyID,
			&i.DistanceMinKm,
			&i.DistanceMaxKm,
			&i.FeeCentavos,
			&i.ExternalID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.CompanyName,
			&i.CompanyExternalID,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getShippingFeeForDistance = `-- name: GetShippingFeeForDistance :one
SELECT fee_centavos
FROM company_shipping_rates
WHERE company_id = $1
  AND $2 >= distance_min_km
  AND $2 < distance_max_km
LIMIT 1
`

type GetShippingFeeForDistanceParams struct {
	CompanyID     int32
	DistanceMinKm pgtype.Numeric
}

func (q *Queries) GetShippingFeeForDistance(ctx context.Context, arg GetShippingFeeForDistanceParams) (int32, error) {
	row := q.db.QueryRow(ctx, getShippingFeeForDistance, arg.CompanyID, arg.DistanceMinKm)
	var fee_centavos int32
	err := row.Scan(&fee_centavos)
	return fee_centavos, err
}

const updateCompanyShippingRate = `-- name: UpdateCompanyShippingRate :exec
UPDATE company_shipping_rates
SET 
    distance_min_km = $2,
    distance_max_km = $3,
    fee_centavos = $4,
    updated_at = NOW()
WHERE external_id = $1
`

type UpdateCompanyShippingRateParams struct {
	ExternalID    string
	DistanceMinKm pgtype.Numeric
	DistanceMaxKm pgtype.Numeric
	FeeCentavos   int32
}

func (q *Queries) UpdateCompanyShippingRate(ctx context.Context, arg UpdateCompanyShippingRateParams) error {
	_, err := q.db.Exec(ctx, updateCompanyShippingRate,
		arg.ExternalID,
		arg.DistanceMinKm,
		arg.DistanceMaxKm,
		arg.FeeCentavos,
	)
	return err
}

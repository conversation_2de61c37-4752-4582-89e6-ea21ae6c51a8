// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: users.sql

package postgres

import (
	"context"
	"database/sql"
	"time"

	"github.com/jackc/pgtype"
)

const assignRoleToUser = `-- name: AssignRoleToUser :exec
INSERT INTO user_roles (user_id, role_id)
SELECT $1, r.id FROM roles r WHERE r.name = $2
ON CONFLICT (user_id, role_id) DO NOTHING
`

type AssignRoleToUserParams struct {
	UserID int32
	Name   string
}

func (q *Queries) AssignRoleToUser(ctx context.Context, arg AssignRoleToUserParams) error {
	_, err := q.db.Exec(ctx, assignRoleToUser, arg.UserID, arg.Name)
	return err
}

const checkIfUserHasRole = `-- name: CheckIfUserHasRole :one
SELECT EXISTS (
  SELECT 1
  FROM user_roles ur
  JOIN roles r ON ur.role_id = r.id
  WHERE ur.user_id = $1 AND r.name = $2
) AS has_role
`

type CheckIfUserHasRoleParams struct {
	UserID int32
	Name   string
}

func (q *Queries) CheckIfUserHasRole(ctx context.Context, arg CheckIfUserHasRoleParams) (bool, error) {
	row := q.db.QueryRow(ctx, checkIfUserHasRole, arg.UserID, arg.Name)
	var has_role bool
	err := row.Scan(&has_role)
	return has_role, err
}

const checkIfUserIsPartner = `-- name: CheckIfUserIsPartner :one
SELECT EXISTS (
  SELECT 1
  FROM companies
  WHERE owner_id = $1
  AND is_active = true
) AS is_partner
`

func (q *Queries) CheckIfUserIsPartner(ctx context.Context, ownerID sql.NullInt32) (bool, error) {
	row := q.db.QueryRow(ctx, checkIfUserIsPartner, ownerID)
	var is_partner bool
	err := row.Scan(&is_partner)
	return is_partner, err
}

const createUser = `-- name: CreateUser :one
INSERT INTO users (name, email, cpf, login_code, phone_numbers, external_id)
  VALUES ($1, $2, $3, $4, $5, $6) RETURNING external_id, id
`

type CreateUserParams struct {
	Name         string
	Email        string
	Cpf          string
	LoginCode    string
	PhoneNumbers []string
	ExternalID   string
}

type CreateUserRow struct {
	ExternalID string
	ID         int32
}

func (q *Queries) CreateUser(ctx context.Context, arg CreateUserParams) (CreateUserRow, error) {
	row := q.db.QueryRow(ctx, createUser,
		arg.Name,
		arg.Email,
		arg.Cpf,
		arg.LoginCode,
		arg.PhoneNumbers,
		arg.ExternalID,
	)
	var i CreateUserRow
	err := row.Scan(&i.ExternalID, &i.ID)
	return i, err
}

const deleteUserAddress = `-- name: DeleteUserAddress :one
DELETE FROM user_addresses 
WHERE user_addresses.external_id = $1 AND user_addresses.user_id = $2
AND (SELECT COUNT(*) FROM user_addresses WHERE user_id = $2) > 1 RETURNING external_id
`

type DeleteUserAddressParams struct {
	ExternalID string
	UserID     int32
}

func (q *Queries) DeleteUserAddress(ctx context.Context, arg DeleteUserAddressParams) (string, error) {
	row := q.db.QueryRow(ctx, deleteUserAddress, arg.ExternalID, arg.UserID)
	var external_id string
	err := row.Scan(&external_id)
	return external_id, err
}

const disableUser = `-- name: DisableUser :exec
UPDATE users SET is_active = false WHERE external_id = $1
`

func (q *Queries) DisableUser(ctx context.Context, externalID string) error {
	_, err := q.db.Exec(ctx, disableUser, externalID)
	return err
}

const getMe = `-- name: GetMe :one
SELECT
  u.id,
  u.name,
  u.email,
  u.login_code,
  u.cpf,
  u.phone_numbers,
  u.cashback_value,
  u.subscription_id,
  u.is_active,
  u.is_deleted,
  u.external_id,
  u.created_at,
  u.updated_at,
  COALESCE(
    json_agg(
      jsonb_build_object(
        'name', ua.name,
        'street', ua.street,
        'number', ua.number,
        'complement', ua.complement,
        'neighborhood', ua.neighborhood,
        'city', ua.city,
        'state', ua.state,
        'zip_code', ua.zip_code,
        'location', jsonb_build_object(
          'latitude', ua.latitude,
          'longitude', ua.longitude
        ),
        'is_default', ua.is_default,
        'external_id', ua.external_id,
        'created_at', ua.created_at,
        'updated_at', ua.updated_at
      ) ORDER BY ua.updated_at DESC
    ) FILTER (WHERE ua.id IS NOT NULL),
    '[]'
  )::jsonb AS addresses
FROM users u
LEFT JOIN user_addresses ua ON u.id = ua.user_id
WHERE u.id = $1
GROUP BY u.id
`

type GetMeRow struct {
	ID             int32
	Name           string
	Email          string
	LoginCode      string
	Cpf            string
	PhoneNumbers   []string
	CashbackValue  int32
	SubscriptionID sql.NullInt32
	IsActive       bool
	IsDeleted      bool
	ExternalID     string
	CreatedAt      time.Time
	UpdatedAt      time.Time
	Addresses      pgtype.JSONB
}

func (q *Queries) GetMe(ctx context.Context, id int32) (GetMeRow, error) {
	row := q.db.QueryRow(ctx, getMe, id)
	var i GetMeRow
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Email,
		&i.LoginCode,
		&i.Cpf,
		&i.PhoneNumbers,
		&i.CashbackValue,
		&i.SubscriptionID,
		&i.IsActive,
		&i.IsDeleted,
		&i.ExternalID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Addresses,
	)
	return i, err
}

const getNearestCompaniesToUserAddress = `-- name: GetNearestCompaniesToUserAddress :many
SELECT
  c.id AS company_id,
  c.name AS company_name,
  ua.id AS user_address_id,
  ST_Distance(c.location, ua.location) AS distance_meters
FROM companies c
JOIN user_addresses ua ON ST_DWithin(c.location, ua.location, $1::int4)
WHERE ua.user_id = $2
ORDER BY ua.id, distance_meters ASC
`

type GetNearestCompaniesToUserAddressParams struct {
	Column1 int32
	UserID  int32
}

type GetNearestCompaniesToUserAddressRow struct {
	CompanyID      int32
	CompanyName    string
	UserAddressID  int32
	DistanceMeters interface{}
}

func (q *Queries) GetNearestCompaniesToUserAddress(ctx context.Context, arg GetNearestCompaniesToUserAddressParams) ([]GetNearestCompaniesToUserAddressRow, error) {
	rows, err := q.db.Query(ctx, getNearestCompaniesToUserAddress, arg.Column1, arg.UserID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetNearestCompaniesToUserAddressRow
	for rows.Next() {
		var i GetNearestCompaniesToUserAddressRow
		if err := rows.Scan(
			&i.CompanyID,
			&i.CompanyName,
			&i.UserAddressID,
			&i.DistanceMeters,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserAddressByExternalID = `-- name: GetUserAddressByExternalID :one
SELECT
  ua.name,
  ua.street,
  ua.number,
  ua.complement,
  ua.neighborhood,
  ua.city,
  ua.state,
  ua.zip_code
FROM user_addresses ua
WHERE ua.external_id = $1
`

type GetUserAddressByExternalIDRow struct {
	Name         string
	Street       string
	Number       string
	Complement   sql.NullString
	Neighborhood string
	City         string
	State        string
	ZipCode      string
}

func (q *Queries) GetUserAddressByExternalID(ctx context.Context, externalID string) (GetUserAddressByExternalIDRow, error) {
	row := q.db.QueryRow(ctx, getUserAddressByExternalID, externalID)
	var i GetUserAddressByExternalIDRow
	err := row.Scan(
		&i.Name,
		&i.Street,
		&i.Number,
		&i.Complement,
		&i.Neighborhood,
		&i.City,
		&i.State,
		&i.ZipCode,
	)
	return i, err
}

const getUserByEmail = `-- name: GetUserByEmail :one
SELECT id,name, login_code,is_active, is_deleted, external_id FROM users WHERE email = $1
`

type GetUserByEmailRow struct {
	ID         int32
	Name       string
	LoginCode  string
	IsActive   bool
	IsDeleted  bool
	ExternalID string
}

func (q *Queries) GetUserByEmail(ctx context.Context, email string) (GetUserByEmailRow, error) {
	row := q.db.QueryRow(ctx, getUserByEmail, email)
	var i GetUserByEmailRow
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.LoginCode,
		&i.IsActive,
		&i.IsDeleted,
		&i.ExternalID,
	)
	return i, err
}

const getUserByExternalID = `-- name: GetUserByExternalID :one
SELECT
  u.id,
  u.is_active,
  u.name,
  u.email,
  u.cpf,
  u.phone_numbers,
  u.external_id
FROM users u
WHERE u.external_id = $1
`

type GetUserByExternalIDRow struct {
	ID           int32
	IsActive     bool
	Name         string
	Email        string
	Cpf          string
	PhoneNumbers []string
	ExternalID   string
}

func (q *Queries) GetUserByExternalID(ctx context.Context, externalID string) (GetUserByExternalIDRow, error) {
	row := q.db.QueryRow(ctx, getUserByExternalID, externalID)
	var i GetUserByExternalIDRow
	err := row.Scan(
		&i.ID,
		&i.IsActive,
		&i.Name,
		&i.Email,
		&i.Cpf,
		&i.PhoneNumbers,
		&i.ExternalID,
	)
	return i, err
}

const getUserByID = `-- name: GetUserByID :one
SELECT id, is_active, name, email FROM users WHERE id = $1
`

type GetUserByIDRow struct {
	ID       int32
	IsActive bool
	Name     string
	Email    string
}

func (q *Queries) GetUserByID(ctx context.Context, id int32) (GetUserByIDRow, error) {
	row := q.db.QueryRow(ctx, getUserByID, id)
	var i GetUserByIDRow
	err := row.Scan(
		&i.ID,
		&i.IsActive,
		&i.Name,
		&i.Email,
	)
	return i, err
}

const getUserRoles = `-- name: GetUserRoles :many
SELECT r.name
FROM users u
JOIN user_roles ur ON u.id = ur.user_id
JOIN roles r ON ur.role_id = r.id
WHERE u.external_id = $1
`

func (q *Queries) GetUserRoles(ctx context.Context, externalID string) ([]string, error) {
	rows, err := q.db.Query(ctx, getUserRoles, externalID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []string
	for rows.Next() {
		var name string
		if err := rows.Scan(&name); err != nil {
			return nil, err
		}
		items = append(items, name)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const removeRoleFromUser = `-- name: RemoveRoleFromUser :exec
DELETE FROM user_roles
WHERE user_id = $1 AND role_id = (SELECT id FROM roles WHERE name = $2)
`

type RemoveRoleFromUserParams struct {
	UserID int32
	Name   string
}

func (q *Queries) RemoveRoleFromUser(ctx context.Context, arg RemoveRoleFromUserParams) error {
	_, err := q.db.Exec(ctx, removeRoleFromUser, arg.UserID, arg.Name)
	return err
}

const resetUserAddressDefault = `-- name: ResetUserAddressDefault :exec
UPDATE user_addresses
SET is_default = false
WHERE user_id = $1 AND is_default = true
`

func (q *Queries) ResetUserAddressDefault(ctx context.Context, userID int32) error {
	_, err := q.db.Exec(ctx, resetUserAddressDefault, userID)
	return err
}

const searchUsers = `-- name: SearchUsers :many
SELECT
  u.external_id,
  u.name,
  u.email,
  u.cpf,
  u.phone_numbers,
  u.cashback_value,
  u.subscription_id,
  u.is_active,
  u.is_deleted,
  u.created_at,
  u.updated_at,
  COUNT(*) OVER() AS total_count
FROM users u
WHERE (
  u.cpf ILIKE '%' || unaccent($3::text) || '%' OR
  unaccent(u.name) ILIKE '%' || unaccent($3::text) || '%' OR
  unaccent(u.email) ILIKE '%' || unaccent($3::text) || '%'
)
ORDER BY u.created_at DESC
LIMIT $1 OFFSET $2
`

type SearchUsersParams struct {
	Limit       int32
	Offset      int32
	SearchQuery string
}

type SearchUsersRow struct {
	ExternalID     string
	Name           string
	Email          string
	Cpf            string
	PhoneNumbers   []string
	CashbackValue  int32
	SubscriptionID sql.NullInt32
	IsActive       bool
	IsDeleted      bool
	CreatedAt      time.Time
	UpdatedAt      time.Time
	TotalCount     int64
}

func (q *Queries) SearchUsers(ctx context.Context, arg SearchUsersParams) ([]SearchUsersRow, error) {
	rows, err := q.db.Query(ctx, searchUsers, arg.Limit, arg.Offset, arg.SearchQuery)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []SearchUsersRow
	for rows.Next() {
		var i SearchUsersRow
		if err := rows.Scan(
			&i.ExternalID,
			&i.Name,
			&i.Email,
			&i.Cpf,
			&i.PhoneNumbers,
			&i.CashbackValue,
			&i.SubscriptionID,
			&i.IsActive,
			&i.IsDeleted,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.TotalCount,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const setAllUserAddressesToFalse = `-- name: SetAllUserAddressesToFalse :exec
UPDATE user_addresses 
SET is_default = false 
WHERE user_id = $1
`

func (q *Queries) SetAllUserAddressesToFalse(ctx context.Context, userID int32) error {
	_, err := q.db.Exec(ctx, setAllUserAddressesToFalse, userID)
	return err
}

const softDeleteUser = `-- name: SoftDeleteUser :exec
UPDATE users SET is_deleted = true, is_active = false WHERE id = $1
`

func (q *Queries) SoftDeleteUser(ctx context.Context, id int32) error {
	_, err := q.db.Exec(ctx, softDeleteUser, id)
	return err
}

const updateLoginCode = `-- name: UpdateLoginCode :exec
UPDATE users SET login_code = $1 WHERE id = $2
`

type UpdateLoginCodeParams struct {
	LoginCode string
	ID        int32
}

func (q *Queries) UpdateLoginCode(ctx context.Context, arg UpdateLoginCodeParams) error {
	_, err := q.db.Exec(ctx, updateLoginCode, arg.LoginCode, arg.ID)
	return err
}

const updateUserName = `-- name: UpdateUserName :exec
UPDATE users SET name = $1 WHERE id = $2
`

type UpdateUserNameParams struct {
	Name string
	ID   int32
}

func (q *Queries) UpdateUserName(ctx context.Context, arg UpdateUserNameParams) error {
	_, err := q.db.Exec(ctx, updateUserName, arg.Name, arg.ID)
	return err
}

const updateUserStatus = `-- name: UpdateUserStatus :one
UPDATE users
SET is_active = $2, updated_at = now()
WHERE external_id = $1
RETURNING id, name, external_id, is_active
`

type UpdateUserStatusParams struct {
	ExternalID string
	IsActive   bool
}

type UpdateUserStatusRow struct {
	ID         int32
	Name       string
	ExternalID string
	IsActive   bool
}

func (q *Queries) UpdateUserStatus(ctx context.Context, arg UpdateUserStatusParams) (UpdateUserStatusRow, error) {
	row := q.db.QueryRow(ctx, updateUserStatus, arg.ExternalID, arg.IsActive)
	var i UpdateUserStatusRow
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.ExternalID,
		&i.IsActive,
	)
	return i, err
}

const upsertUserAddressByDetails = `-- name: UpsertUserAddressByDetails :one
INSERT INTO user_addresses (
  user_id, name, street, number, complement, neighborhood, city, state, zip_code,
  latitude, longitude, location, external_id, is_default
)
VALUES (
  $1,
  $2,
  $3,
  $4,
  $5,
  $6,
  $7,
  $8,
  $9,
  $10,
  $11,
  ST_SetSRID(ST_MakePoint($11, $10), 4326),
  $12,
  $13
)
ON CONFLICT (user_id, external_id)
DO UPDATE SET
  user_id = EXCLUDED.user_id,
  name = EXCLUDED.name,
  street = EXCLUDED.street,
  number = EXCLUDED.number,
  complement = EXCLUDED.complement,
  neighborhood = EXCLUDED.neighborhood,
  city = EXCLUDED.city,
  state = EXCLUDED.state,
  zip_code = EXCLUDED.zip_code,
  latitude = EXCLUDED.latitude,
  longitude = EXCLUDED.longitude,
  location = EXCLUDED.location,
  is_default = EXCLUDED.is_default,
  external_id = EXCLUDED.external_id,
  updated_at = now()
RETURNING external_id
`

type UpsertUserAddressByDetailsParams struct {
	UserID       int32
	Name         string
	Street       string
	Number       string
	Complement   sql.NullString
	Neighborhood string
	City         string
	State        string
	ZipCode      string
	Latitude     sql.NullFloat64
	Longitude    sql.NullFloat64
	ExternalID   string
	IsDefault    bool
}

func (q *Queries) UpsertUserAddressByDetails(ctx context.Context, arg UpsertUserAddressByDetailsParams) (string, error) {
	row := q.db.QueryRow(ctx, upsertUserAddressByDetails,
		arg.UserID,
		arg.Name,
		arg.Street,
		arg.Number,
		arg.Complement,
		arg.Neighborhood,
		arg.City,
		arg.State,
		arg.ZipCode,
		arg.Latitude,
		arg.Longitude,
		arg.ExternalID,
		arg.IsDefault,
	)
	var external_id string
	err := row.Scan(&external_id)
	return external_id, err
}

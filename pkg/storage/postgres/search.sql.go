// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: search.sql

package postgres

import (
	"context"
	"database/sql"

	"github.com/jackc/pgtype"
)

const fullTextSearchProduct = `-- name: FullTextSearchProduct :many
WITH search_query AS (
  SELECT plainto_tsquery('simple', unaccent($1)) AS query,
         '%' || unaccent($1) || '%' AS ilike_pattern
),
product_results AS (
  SELECT
    p.id AS product_id,
    p.name AS product_name,
    p.external_id AS product_external_id,
    p.image AS product_image,
    p.brand AS product_brand,
    p.is_18_plus AS is_18_plus,
    ts_rank(
      setweight(to_tsvector('simple', unaccent(coalesce(p.name, ''))), 'A') ||
      setweight(to_tsvector('simple', unaccent(coalesce(p.brand, ''))), 'B'),
      sq.query
    ) AS rank
  FROM products p, search_query sq
  WHERE
    p.is_active = true AND
    p.is_reviewed = true AND (
      (
        setweight(to_tsvector('simple', unaccent(coalesce(p.name, ''))), 'A') ||
        setweight(to_tsvector('simple', unaccent(coalesce(p.brand, ''))), 'B')
      ) @@ sq.query
      OR unaccent(p.name) ILIKE sq.ilike_pattern
      OR unaccent(p.brand) ILIKE sq.ilike_pattern
    )
),
results_with_categories AS (
  SELECT
    pr.product_name,
    pr.product_external_id,
    pr.product_image,
    pr.product_brand,
    pr.is_18_plus,
    c.name AS category_name,
    c.image AS category_image,
    c.external_id AS category_external_id,
    pr.rank
  FROM product_results pr
  LEFT JOIN products_categories pc ON pc.product_id = pr.product_id
  LEFT JOIN categories c ON c.id = pc.category_id
)
SELECT json_build_object(
  'product_name', product_name,
  'product_image', product_image,
  'product_brand', product_brand,
  'product_external_id', product_external_id,
  'product_is_18_plus', is_18_plus,
  'category_name', category_name,
  'category_image', category_image,
  'category_external_id', category_external_id,
  'search_rank', rank
)
FROM results_with_categories
ORDER BY rank DESC
LIMIT 3
`

// Legacy endpoint for backward compatibility
func (q *Queries) FullTextSearchProduct(ctx context.Context, unaccent string) ([]pgtype.JSON, error) {
	rows, err := q.db.Query(ctx, fullTextSearchProduct, unaccent)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []pgtype.JSON
	for rows.Next() {
		var json_build_object pgtype.JSON
		if err := rows.Scan(&json_build_object); err != nil {
			return nil, err
		}
		items = append(items, json_build_object)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const searchByCategory = `-- name: SearchByCategory :many
WITH search_query AS (
  SELECT plainto_tsquery('simple', unaccent($1)) AS query,
         '%' || unaccent($1) || '%' AS ilike_pattern
),
product_results AS (
  SELECT
    p.id AS product_id,
    p.name AS product_name,
    p.external_id AS product_external_id,
    p.image AS product_image,
    p.brand AS product_brand,
    p.is_18_plus AS is_18_plus,
    ts_rank(
      setweight(to_tsvector('simple', unaccent(coalesce(p.name, ''))), 'A') ||
      setweight(to_tsvector('simple', unaccent(coalesce(p.brand, ''))), 'B'),
      sq.query
    ) AS rank
  FROM products p
  CROSS JOIN search_query sq
  JOIN products_categories pc ON p.id = pc.product_id
  JOIN categories cat ON pc.category_id = cat.id
  WHERE
    p.is_active = true AND
    p.is_reviewed = true AND
    cat.external_id = $2 AND (
      (
        setweight(to_tsvector('simple', unaccent(coalesce(p.name, ''))), 'A') ||
        setweight(to_tsvector('simple', unaccent(coalesce(p.brand, ''))), 'B')
      ) @@ sq.query
      OR unaccent(p.name) ILIKE sq.ilike_pattern
      OR unaccent(p.brand) ILIKE sq.ilike_pattern
    )
),
results_with_categories AS (
  SELECT
    pr.product_name,
    pr.product_external_id,
    pr.product_image,
    pr.product_brand,
    pr.is_18_plus,
    c.name AS category_name,
    c.image AS category_image,
    c.external_id AS category_external_id,
    pr.rank,
    COUNT(*) OVER() AS total_count
  FROM product_results pr
  LEFT JOIN products_categories pc ON pc.product_id = pr.product_id
  LEFT JOIN categories c ON c.id = pc.category_id
)
SELECT
  product_name,
  product_external_id,
  product_image,
  product_brand,
  is_18_plus,
  category_name,
  category_image,
  category_external_id,
  rank AS search_rank,
  total_count
FROM results_with_categories
ORDER BY rank DESC
LIMIT $3 OFFSET $4
`

type SearchByCategoryParams struct {
	Unaccent   string
	ExternalID string
	Limit      int32
	Offset     int32
}

type SearchByCategoryRow struct {
	ProductName        string
	ProductExternalID  string
	ProductImage       sql.NullString
	ProductBrand       sql.NullString
	Is18Plus           bool
	CategoryName       sql.NullString
	CategoryImage      sql.NullString
	CategoryExternalID sql.NullString
	SearchRank         float32
	TotalCount         int64
}

// Category search - copy of working legacy query with category filter
func (q *Queries) SearchByCategory(ctx context.Context, arg SearchByCategoryParams) ([]SearchByCategoryRow, error) {
	rows, err := q.db.Query(ctx, searchByCategory,
		arg.Unaccent,
		arg.ExternalID,
		arg.Limit,
		arg.Offset,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []SearchByCategoryRow
	for rows.Next() {
		var i SearchByCategoryRow
		if err := rows.Scan(
			&i.ProductName,
			&i.ProductExternalID,
			&i.ProductImage,
			&i.ProductBrand,
			&i.Is18Plus,
			&i.CategoryName,
			&i.CategoryImage,
			&i.CategoryExternalID,
			&i.SearchRank,
			&i.TotalCount,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const searchByCompany = `-- name: SearchByCompany :many
WITH search_query AS (
  SELECT plainto_tsquery('simple', unaccent($1)) AS query,
         '%' || unaccent($1) || '%' AS ilike_pattern
),
product_results AS (
  SELECT
    p.id AS product_id,
    p.name AS product_name,
    p.external_id AS product_external_id,
    p.image AS product_image,
    p.brand AS product_brand,
    p.is_18_plus AS is_18_plus,
    cp.price,
    cp.discount,
    cp.stock,
    comp.name AS company_name,
    comp.external_id AS company_external_id,
    ts_rank(
      setweight(to_tsvector('simple', unaccent(coalesce(p.name, ''))), 'A') ||
      setweight(to_tsvector('simple', unaccent(coalesce(p.brand, ''))), 'B'),
      sq.query
    ) AS rank
  FROM products p
  CROSS JOIN search_query sq
  JOIN company_products cp ON p.id = cp.product_id
  JOIN companies comp ON cp.company_id = comp.id
  WHERE
    p.is_active = true AND
    p.is_reviewed = true AND
    comp.external_id = $2 AND
    comp.is_active = true AND
    cp.stock > 0 AND (
      (
        setweight(to_tsvector('simple', unaccent(coalesce(p.name, ''))), 'A') ||
        setweight(to_tsvector('simple', unaccent(coalesce(p.brand, ''))), 'B')
      ) @@ sq.query
      OR unaccent(p.name) ILIKE sq.ilike_pattern
      OR unaccent(p.brand) ILIKE sq.ilike_pattern
    )
),
results_with_categories AS (
  SELECT
    pr.product_name,
    pr.product_external_id,
    pr.product_image,
    pr.product_brand,
    pr.is_18_plus,
    pr.price,
    pr.discount,
    pr.stock,
    pr.company_name,
    pr.company_external_id,
    c.name AS category_name,
    c.image AS category_image,
    c.external_id AS category_external_id,
    pr.rank,
    COUNT(*) OVER() AS total_count
  FROM product_results pr
  LEFT JOIN products_categories pc ON pc.product_id = pr.product_id
  LEFT JOIN categories c ON c.id = pc.category_id
)
SELECT
  product_name,
  product_external_id,
  product_image,
  product_brand,
  is_18_plus,
  price,
  discount,
  stock,
  company_name,
  company_external_id,
  category_name,
  category_image,
  category_external_id,
  rank AS search_rank,
  total_count
FROM results_with_categories
ORDER BY rank DESC
LIMIT $3 OFFSET $4
`

type SearchByCompanyParams struct {
	Unaccent   string
	ExternalID string
	Limit      int32
	Offset     int32
}

type SearchByCompanyRow struct {
	ProductName        string
	ProductExternalID  string
	ProductImage       sql.NullString
	ProductBrand       sql.NullString
	Is18Plus           bool
	Price              pgtype.Numeric
	Discount           pgtype.Numeric
	Stock              int32
	CompanyName        string
	CompanyExternalID  string
	CategoryName       sql.NullString
	CategoryImage      sql.NullString
	CategoryExternalID sql.NullString
	SearchRank         float32
	TotalCount         int64
}

// Company search - global search + company pricing/stock
func (q *Queries) SearchByCompany(ctx context.Context, arg SearchByCompanyParams) ([]SearchByCompanyRow, error) {
	rows, err := q.db.Query(ctx, searchByCompany,
		arg.Unaccent,
		arg.ExternalID,
		arg.Limit,
		arg.Offset,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []SearchByCompanyRow
	for rows.Next() {
		var i SearchByCompanyRow
		if err := rows.Scan(
			&i.ProductName,
			&i.ProductExternalID,
			&i.ProductImage,
			&i.ProductBrand,
			&i.Is18Plus,
			&i.Price,
			&i.Discount,
			&i.Stock,
			&i.CompanyName,
			&i.CompanyExternalID,
			&i.CategoryName,
			&i.CategoryImage,
			&i.CategoryExternalID,
			&i.SearchRank,
			&i.TotalCount,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const searchByCompanyAndCategory = `-- name: SearchByCompanyAndCategory :many
WITH search_query AS (
  SELECT plainto_tsquery('simple', unaccent($1)) AS query,
         '%' || unaccent($1) || '%' AS ilike_pattern
),
product_results AS (
  SELECT
    p.id AS product_id,
    p.name AS product_name,
    p.external_id AS product_external_id,
    p.image AS product_image,
    p.brand AS product_brand,
    p.is_18_plus AS is_18_plus,
    cp.price,
    cp.discount,
    cp.stock,
    comp.name AS company_name,
    comp.external_id AS company_external_id,
    ts_rank(
      setweight(to_tsvector('simple', unaccent(coalesce(p.name, ''))), 'A') ||
      setweight(to_tsvector('simple', unaccent(coalesce(p.brand, ''))), 'B'),
      sq.query
    ) AS rank
  FROM products p
  CROSS JOIN search_query sq
  JOIN company_products cp ON p.id = cp.product_id
  JOIN companies comp ON cp.company_id = comp.id
  JOIN products_categories pc ON p.id = pc.product_id
  JOIN categories cat ON pc.category_id = cat.id
  WHERE
    p.is_active = true AND
    p.is_reviewed = true AND
    comp.external_id = $2 AND
    cat.external_id = $3 AND
    comp.is_active = true AND
    cp.stock > 0 AND (
      (
        setweight(to_tsvector('simple', unaccent(coalesce(p.name, ''))), 'A') ||
        setweight(to_tsvector('simple', unaccent(coalesce(p.brand, ''))), 'B')
      ) @@ sq.query
      OR unaccent(p.name) ILIKE sq.ilike_pattern
      OR unaccent(p.brand) ILIKE sq.ilike_pattern
    )
),
results_with_categories AS (
  SELECT
    pr.product_name,
    pr.product_external_id,
    pr.product_image,
    pr.product_brand,
    pr.is_18_plus,
    pr.price,
    pr.discount,
    pr.stock,
    pr.company_name,
    pr.company_external_id,
    c.name AS category_name,
    c.image AS category_image,
    c.external_id AS category_external_id,
    pr.rank,
    COUNT(*) OVER() AS total_count
  FROM product_results pr
  LEFT JOIN products_categories pc ON pc.product_id = pr.product_id
  LEFT JOIN categories c ON c.id = pc.category_id
)
SELECT
  product_name,
  product_external_id,
  product_image,
  product_brand,
  is_18_plus,
  price,
  discount,
  stock,
  company_name,
  company_external_id,
  category_name,
  category_image,
  category_external_id,
  rank AS search_rank,
  total_count
FROM results_with_categories
ORDER BY rank DESC
LIMIT $4 OFFSET $5
`

type SearchByCompanyAndCategoryParams struct {
	Unaccent     string
	ExternalID   string
	ExternalID_2 string
	Limit        int32
	Offset       int32
}

type SearchByCompanyAndCategoryRow struct {
	ProductName        string
	ProductExternalID  string
	ProductImage       sql.NullString
	ProductBrand       sql.NullString
	Is18Plus           bool
	Price              pgtype.Numeric
	Discount           pgtype.Numeric
	Stock              int32
	CompanyName        string
	CompanyExternalID  string
	CategoryName       sql.NullString
	CategoryImage      sql.NullString
	CategoryExternalID sql.NullString
	SearchRank         float32
	TotalCount         int64
}

// Company + Category search - company search + category filter
func (q *Queries) SearchByCompanyAndCategory(ctx context.Context, arg SearchByCompanyAndCategoryParams) ([]SearchByCompanyAndCategoryRow, error) {
	rows, err := q.db.Query(ctx, searchByCompanyAndCategory,
		arg.Unaccent,
		arg.ExternalID,
		arg.ExternalID_2,
		arg.Limit,
		arg.Offset,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []SearchByCompanyAndCategoryRow
	for rows.Next() {
		var i SearchByCompanyAndCategoryRow
		if err := rows.Scan(
			&i.ProductName,
			&i.ProductExternalID,
			&i.ProductImage,
			&i.ProductBrand,
			&i.Is18Plus,
			&i.Price,
			&i.Discount,
			&i.Stock,
			&i.CompanyName,
			&i.CompanyExternalID,
			&i.CategoryName,
			&i.CategoryImage,
			&i.CategoryExternalID,
			&i.SearchRank,
			&i.TotalCount,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const searchGlobal = `-- name: SearchGlobal :many
WITH search_query AS (
  SELECT plainto_tsquery('simple', unaccent($1)) AS query,
         '%' || unaccent($1) || '%' AS ilike_pattern
),
product_results AS (
  SELECT
    p.id AS product_id,
    p.name AS product_name,
    p.external_id AS product_external_id,
    p.image AS product_image,
    p.brand AS product_brand,
    p.is_18_plus AS is_18_plus,
    ts_rank(
      setweight(to_tsvector('simple', unaccent(coalesce(p.name, ''))), 'A') ||
      setweight(to_tsvector('simple', unaccent(coalesce(p.brand, ''))), 'B'),
      sq.query
    ) AS rank
  FROM products p, search_query sq
  WHERE
    p.is_active = true AND
    p.is_reviewed = true AND (
      (
        setweight(to_tsvector('simple', unaccent(coalesce(p.name, ''))), 'A') ||
        setweight(to_tsvector('simple', unaccent(coalesce(p.brand, ''))), 'B')
      ) @@ sq.query
      OR unaccent(p.name) ILIKE sq.ilike_pattern
      OR unaccent(p.brand) ILIKE sq.ilike_pattern
    )
),
results_with_categories AS (
  SELECT
    pr.product_name,
    pr.product_external_id,
    pr.product_image,
    pr.product_brand,
    pr.is_18_plus,
    c.name AS category_name,
    c.image AS category_image,
    c.external_id AS category_external_id,
    pr.rank,
    COUNT(*) OVER() AS total_count
  FROM product_results pr
  LEFT JOIN products_categories pc ON pc.product_id = pr.product_id
  LEFT JOIN categories c ON c.id = pc.category_id
)
SELECT
  product_name,
  product_external_id,
  product_image,
  product_brand,
  is_18_plus,
  category_name,
  category_image,
  category_external_id,
  rank AS search_rank,
  total_count
FROM results_with_categories
ORDER BY rank DESC
LIMIT $2 OFFSET $3
`

type SearchGlobalParams struct {
	Unaccent string
	Limit    int32
	Offset   int32
}

type SearchGlobalRow struct {
	ProductName        string
	ProductExternalID  string
	ProductImage       sql.NullString
	ProductBrand       sql.NullString
	Is18Plus           bool
	CategoryName       sql.NullString
	CategoryImage      sql.NullString
	CategoryExternalID sql.NullString
	SearchRank         float32
	TotalCount         int64
}

// Global search - exactly like legacy but with pagination
func (q *Queries) SearchGlobal(ctx context.Context, arg SearchGlobalParams) ([]SearchGlobalRow, error) {
	rows, err := q.db.Query(ctx, searchGlobal, arg.Unaccent, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []SearchGlobalRow
	for rows.Next() {
		var i SearchGlobalRow
		if err := rows.Scan(
			&i.ProductName,
			&i.ProductExternalID,
			&i.ProductImage,
			&i.ProductBrand,
			&i.Is18Plus,
			&i.CategoryName,
			&i.CategoryImage,
			&i.CategoryExternalID,
			&i.SearchRank,
			&i.TotalCount,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: min_mobile_version.sql

package postgres

import (
	"context"
)

const getMinMobileVersion = `-- name: GetMinMobileVersion :one
SELECT ios_version, android_version, is_active FROM min_mobile_version ORDER BY created_at DESC LIMIT 1
`

type GetMinMobileVersionRow struct {
	IosVersion     string
	AndroidVersion string
	IsActive       bool
}

func (q *Queries) GetMinMobileVersion(ctx context.Context) (GetMinMobileVersionRow, error) {
	row := q.db.QueryRow(ctx, getMinMobileVersion)
	var i GetMinMobileVersionRow
	err := row.Scan(&i.IosVersion, &i.AndroidVersion, &i.IsActive)
	return i, err
}

const handleMinMobileVersionActivation = `-- name: HandleMinMobileVersionActivation :exec
UPDATE min_mobile_version SET is_active = $1 WHERE id = 1
`

func (q *Queries) HandleMinMobileVersionActivation(ctx context.Context, isActive bool) error {
	_, err := q.db.Exec(ctx, handleMinMobileVersionActivation, isActive)
	return err
}

const upsertMinMobileVersion = `-- name: UpsertMinMobileVersion :exec
INSERT INTO min_mobile_version (id, ios_version, android_version)
VALUES (1, $1, $2)
ON CONFLICT (id) DO UPDATE
SET ios_version = EXCLUDED.ios_version,
    android_version = EXCLUDED.android_version,
    updated_at = NOW()
`

type UpsertMinMobileVersionParams struct {
	IosVersion     string
	AndroidVersion string
}

func (q *Queries) UpsertMinMobileVersion(ctx context.Context, arg UpsertMinMobileVersionParams) error {
	_, err := q.db.Exec(ctx, upsertMinMobileVersion, arg.IosVersion, arg.AndroidVersion)
	return err
}

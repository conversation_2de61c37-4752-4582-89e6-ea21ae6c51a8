-- Add max_shipping_distance_km field to companies table
ALTER TABLE companies 
ADD COLUMN max_shipping_distance_km DECIMAL(5,2) DEFAULT 10.0 NOT NULL;

-- Create index for performance optimization
CREATE INDEX idx_companies_max_shipping_distance 
ON companies (max_shipping_distance_km) 
WHERE is_active = true;

-- Populate existing data with calculated max shipping distances
UPDATE companies 
SET max_shipping_distance_km = COALESCE(
    (SELECT MAX(distance_max_km) 
     FROM company_shipping_rates 
     WHERE company_id = companies.id), 
    10.0
);

-- Create function to sync max shipping distance when shipping rates change
CREATE OR REPLACE FUNCTION sync_company_max_shipping_distance()
RETURNS TRIGGER AS $$
BEGIN
    -- Update the company's max shipping distance based on current shipping rates
    UPDATE companies 
    SET max_shipping_distance_km = COALESCE(
        (SELECT MAX(distance_max_km) 
         FROM company_shipping_rates 
         WHERE company_id = COALESCE(NEW.company_id, OLD.company_id)),
        10.0
    )
    WHERE id = COALESCE(NEW.company_id, OLD.company_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically sync max shipping distance
CREATE TRIGGER trigger_sync_max_shipping_distance
    AFTER INSERT OR UPDATE OR DELETE ON company_shipping_rates
    FOR EACH ROW EXECUTE FUNCTION sync_company_max_shipping_distance();

-- Create function to verify shipping distance consistency
CREATE OR REPLACE FUNCTION verify_shipping_distance_consistency()
RETURNS TABLE(company_id INT, calculated DECIMAL, stored DECIMAL) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.id,
        COALESCE(MAX(csr.distance_max_km), 10.0) as calculated,
        c.max_shipping_distance_km as stored
    FROM companies c
    LEFT JOIN company_shipping_rates csr ON c.id = csr.company_id
    GROUP BY c.id, c.max_shipping_distance_km
    HAVING COALESCE(MAX(csr.distance_max_km), 10.0) != c.max_shipping_distance_km;
END;
$$ LANGUAGE plpgsql;

-- Create function to fix inconsistent shipping distances
CREATE OR REPLACE FUNCTION fix_shipping_distance_inconsistencies()
RETURNS INT AS $$
DECLARE
    fixed_count INT := 0;
BEGIN
    UPDATE companies 
    SET max_shipping_distance_km = COALESCE(
        (SELECT MAX(distance_max_km) 
         FROM company_shipping_rates 
         WHERE company_id = companies.id), 
        10.0
    )
    WHERE id IN (
        SELECT company_id FROM verify_shipping_distance_consistency()
    );
    
    GET DIAGNOSTICS fixed_count = ROW_COUNT;
    RETURN fixed_count;
END;
$$ LANGUAGE plpgsql;

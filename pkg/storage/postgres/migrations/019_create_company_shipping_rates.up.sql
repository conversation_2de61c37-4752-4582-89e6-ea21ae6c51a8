-- Create company_shipping_rates table for distance-based shipping fee calculation
CREATE TABLE IF NOT EXISTS company_shipping_rates (
    id SERIAL PRIMARY KEY,
    company_id INTEGER NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    distance_min_km DECIMAL(5,2) NOT NULL CHECK (distance_min_km >= 0),
    distance_max_km DECIMAL(5,2) NOT NULL CHECK (distance_max_km > distance_min_km),
    fee_centavos INTEGER NOT NULL CHECK (fee_centavos >= 0),
    external_id TEXT NOT NULL UNIQUE,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    
    -- Ensure no overlapping distance ranges for the same company
    CONSTRAINT unique_company_distance_range UNIQUE (company_id, distance_min_km, distance_max_km),
    
    -- Ensure logical distance range ordering
    CONSTRAINT valid_distance_range CHECK (distance_max_km > distance_min_km)
);

-- Create indexes for efficient querying
CREATE INDEX idx_company_shipping_rates_company_id ON company_shipping_rates(company_id);
CREATE INDEX idx_company_shipping_rates_distance_range ON company_shipping_rates(company_id, distance_min_km, distance_max_km);
CREATE INDEX idx_company_shipping_rates_external_id ON company_shipping_rates(external_id);

-- Add comment for documentation
COMMENT ON TABLE company_shipping_rates IS 'Distance-based shipping rates for companies. Each company can define custom distance ranges and corresponding fees.';
COMMENT ON COLUMN company_shipping_rates.distance_min_km IS 'Minimum distance in kilometers (inclusive)';
COMMENT ON COLUMN company_shipping_rates.distance_max_km IS 'Maximum distance in kilometers (exclusive)';
COMMENT ON COLUMN company_shipping_rates.fee_centavos IS 'Shipping fee in centavos (e.g., 500 = R$5.00)';

-- Create function to prevent overlapping distance ranges for the same company
CREATE OR REPLACE FUNCTION check_no_overlapping_shipping_rates()
RETURNS TRIGGER AS $$
BEGIN
    -- Check for overlapping ranges with existing records
    IF EXISTS (
        SELECT 1 FROM company_shipping_rates 
        WHERE company_id = NEW.company_id 
        AND id != COALESCE(NEW.id, -1)  -- Exclude current record for updates
        AND (
            -- New range overlaps with existing range
            (NEW.distance_min_km < distance_max_km AND NEW.distance_max_km > distance_min_km)
        )
    ) THEN
        RAISE EXCEPTION 'Distance range %.2f-%.2f km overlaps with existing shipping rate for company %', 
            NEW.distance_min_km, NEW.distance_max_km, NEW.company_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to enforce no overlapping ranges
CREATE TRIGGER trigger_check_no_overlapping_shipping_rates
    BEFORE INSERT OR UPDATE ON company_shipping_rates
    FOR EACH ROW
    EXECUTE FUNCTION check_no_overlapping_shipping_rates();

-- Insert default shipping rates for existing companies
-- This will create a single rate range (0-10km) using their current shipping_fee
INSERT INTO company_shipping_rates (company_id, distance_min_km, distance_max_km, fee_centavos, external_id)
SELECT 
    c.id,
    0.00,
    10,
    c.shipping_fee,
    'default_' || c.external_id || '_' || extract(epoch from now())::text
FROM companies c
WHERE c.is_active = true
ON CONFLICT DO NOTHING;

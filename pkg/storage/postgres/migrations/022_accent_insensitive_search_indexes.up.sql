-- Migration: Add accent-insensitive search indexes
-- This migration creates optimized indexes for accent-insensitive search functionality
-- using the unaccent() function with both full-text search and ILIKE pattern matching

-- =====================================================================================
-- 📌 ACCENT-INSENSITIVE FULL-TEXT SEARCH INDEXES
-- =====================================================================================
-- These indexes support the new unaccent() + to_tsvector('simple', ...) search pattern
-- =====================================================================================

-- GIN index for accent-insensitive full-text search on product names
-- HELPS: Full-text search queries using to_tsvector('simple', unaccent(name))
-- WHY: Enables fast full-text search without accent sensitivity (feijao finds feijão)
-- IMPACT: Dramatically improves search accuracy for Portuguese text with accents
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_name_unaccent_gin 
ON products USING gin(to_tsvector('simple', unaccent(name)));

-- GIN index for accent-insensitive full-text search on product brands
-- HELPS: Full-text search queries using to_tsvector('simple', unaccent(brand))
-- WHY: Enables fast brand search without accent sensitivity
-- IMPACT: Improves brand search accuracy for accented brand names
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_brand_unaccent_gin 
ON products USING gin(to_tsvector('simple', unaccent(brand)));

-- =====================================================================================
-- 📌 ACCENT-INSENSITIVE ILIKE PATTERN INDEXES
-- =====================================================================================
-- These indexes support the new unaccent() + ILIKE pattern matching
-- =====================================================================================

-- B-tree index for accent-insensitive ILIKE patterns on product names
-- HELPS: ILIKE queries using unaccent(name) ILIKE pattern
-- WHY: Enables fast pattern matching without accent sensitivity
-- IMPACT: Improves partial word matching (e.g., "feij" finds "feijão")
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_name_unaccent_ilike 
ON products (unaccent(name) text_pattern_ops);

-- B-tree index for accent-insensitive ILIKE patterns on product brands
-- HELPS: ILIKE queries using unaccent(brand) ILIKE pattern
-- WHY: Enables fast brand pattern matching without accent sensitivity
-- IMPACT: Improves partial brand matching accuracy
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_brand_unaccent_ilike 
ON products (unaccent(brand) text_pattern_ops);

-- =====================================================================================
-- 📌 USER SEARCH ACCENT-INSENSITIVE INDEXES
-- =====================================================================================
-- These indexes support accent-insensitive user search functionality
-- =====================================================================================

-- B-tree index for accent-insensitive user name search
-- HELPS: User search queries using unaccent(name) ILIKE pattern
-- WHY: Enables fast user name search without accent sensitivity
-- IMPACT: Improves user search accuracy for names with accents
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_name_unaccent_ilike 
ON users (unaccent(name) text_pattern_ops);

-- B-tree index for accent-insensitive user email search
-- HELPS: User search queries using unaccent(email) ILIKE pattern
-- WHY: Enables fast user email search without accent sensitivity
-- IMPACT: Improves user search accuracy for emails with accented characters
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email_unaccent_ilike 
ON users (unaccent(email) text_pattern_ops);

-- =====================================================================================
-- 📌 PERFORMANCE NOTES
-- =====================================================================================
-- 1. All indexes are created with CONCURRENTLY to avoid blocking production traffic
-- 2. The 'simple' dictionary is used instead of 'portuguese' for more literal matching
-- 3. unaccent() function removes accents: "feijão" becomes "feijao"
-- 4. text_pattern_ops operator class optimizes ILIKE pattern matching performance
-- 5. These indexes work alongside existing indexes, providing fallback options
-- =====================================================================================

-- =====================================================================================
-- 📌 EXPECTED SEARCH IMPROVEMENTS
-- =====================================================================================
-- BEFORE: "feijao" → 0 results (doesn't match "feijão")
-- AFTER:  "feijao" → Finds "feijão", "Feijão Preto", "feijao carioca"
--
-- BEFORE: "jose" → Only exact matches
-- AFTER:  "jose" → Finds "José", "José Silva", "<EMAIL>"
--
-- BEFORE: "acucar" → 0 results (doesn't match "açúcar")
-- AFTER:  "acucar" → Finds "açúcar", "Açúcar Cristal", "acucar refinado"
-- =====================================================================================

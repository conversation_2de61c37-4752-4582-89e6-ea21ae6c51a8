-- name: CreateUser :one
INSERT INTO users (name, email, cpf, login_code, phone_numbers, external_id)
  VALUES ($1, $2, $3, $4, $5, $6) RETURNING external_id, id;

-- name: GetUserByExternalID :one
SELECT
  u.id,
  u.is_active,
  u.name,
  u.email,
  u.cpf,
  u.phone_numbers,
  u.external_id
FROM users u
WHERE u.external_id = $1;

-- name: GetUserRoles :many
SELECT r.name
FROM users u
JOIN user_roles ur ON u.id = ur.user_id
JOIN roles r ON ur.role_id = r.id
WHERE u.external_id = $1;

-- name: GetUserByID :one
SELECT id, is_active, name, email FROM users WHERE id = $1;

-- name: GetUserByEmail :one
SELECT id,name, login_code,is_active, is_deleted, external_id FROM users WHERE email = $1;

-- name: UpdateUserName :exec
UPDATE users SET name = $1 WHERE id = $2;

-- name: DisableUser :exec
UPDATE users SET is_active = false WHERE external_id = $1;

-- name: UpdateUserStatus :one
UPDATE users
SET is_active = $2, updated_at = now()
WHERE external_id = $1
RETURNING id, name, external_id, is_active;

-- name: UpdateLoginCode :exec
UPDATE users SET login_code = $1 WHERE id = $2;

-- name: GetMe :one
SELECT
  u.id,
  u.name,
  u.email,
  u.login_code,
  u.cpf,
  u.phone_numbers,
  u.cashback_value,
  u.subscription_id,
  u.is_active,
  u.is_deleted,
  u.external_id,
  u.created_at,
  u.updated_at,
  COALESCE(
    json_agg(
      jsonb_build_object(
        'name', ua.name,
        'street', ua.street,
        'number', ua.number,
        'complement', ua.complement,
        'neighborhood', ua.neighborhood,
        'city', ua.city,
        'state', ua.state,
        'zip_code', ua.zip_code,
        'location', jsonb_build_object(
          'latitude', ua.latitude,
          'longitude', ua.longitude
        ),
        'is_default', ua.is_default,
        'external_id', ua.external_id,
        'created_at', ua.created_at,
        'updated_at', ua.updated_at
      ) ORDER BY ua.updated_at DESC
    ) FILTER (WHERE ua.id IS NOT NULL),
    '[]'
  )::jsonb AS addresses
FROM users u
LEFT JOIN user_addresses ua ON u.id = ua.user_id
WHERE u.id = $1
GROUP BY u.id;


-- name: GetNearestCompaniesToUserAddress :many
SELECT
  c.id AS company_id,
  c.name AS company_name,
  ua.id AS user_address_id,
  ST_Distance(c.location, ua.location) AS distance_meters
FROM companies c
JOIN user_addresses ua ON ST_DWithin(c.location, ua.location, $1::int4)
WHERE ua.user_id = $2
ORDER BY ua.id, distance_meters ASC;

-- name: ResetUserAddressDefault :exec
UPDATE user_addresses
SET is_default = false
WHERE user_id = $1 AND is_default = true;

-- name: UpsertUserAddressByDetails :one
INSERT INTO user_addresses (
  user_id, name, street, number, complement, neighborhood, city, state, zip_code,
  latitude, longitude, location, external_id, is_default
)
VALUES (
  sqlc.arg(user_id),
  sqlc.arg(name),
  sqlc.arg(street),
  sqlc.arg(number),
  sqlc.arg(complement),
  sqlc.arg(neighborhood),
  sqlc.arg(city),
  sqlc.arg(state),
  sqlc.arg(zip_code),
  sqlc.arg(latitude),
  sqlc.arg(longitude),
  ST_SetSRID(ST_MakePoint(sqlc.arg(longitude), sqlc.arg(latitude)), 4326),
  sqlc.arg(external_id),
  sqlc.arg(is_default)
)
ON CONFLICT (user_id, external_id)
DO UPDATE SET
  user_id = EXCLUDED.user_id,
  name = EXCLUDED.name,
  street = EXCLUDED.street,
  number = EXCLUDED.number,
  complement = EXCLUDED.complement,
  neighborhood = EXCLUDED.neighborhood,
  city = EXCLUDED.city,
  state = EXCLUDED.state,
  zip_code = EXCLUDED.zip_code,
  latitude = EXCLUDED.latitude,
  longitude = EXCLUDED.longitude,
  location = EXCLUDED.location,
  is_default = EXCLUDED.is_default,
  external_id = EXCLUDED.external_id,
  updated_at = now()
RETURNING external_id;


-- name: SetAllUserAddressesToFalse :exec
UPDATE user_addresses 
SET is_default = false 
WHERE user_id = $1;

-- name: DeleteUserAddress :one
DELETE FROM user_addresses 
WHERE user_addresses.external_id = $1 AND user_addresses.user_id = $2
AND (SELECT COUNT(*) FROM user_addresses WHERE user_id = $2) > 1 RETURNING external_id;

-- name: SoftDeleteUser :exec
UPDATE users SET is_deleted = true, is_active = false WHERE id = $1;

-- name: GetUserAddressByExternalID :one
SELECT
  ua.name,
  ua.street,
  ua.number,
  ua.complement,
  ua.neighborhood,
  ua.city,
  ua.state,
  ua.zip_code
FROM user_addresses ua
WHERE ua.external_id = $1;

-- name: CheckIfUserIsPartner :one
SELECT EXISTS (
  SELECT 1
  FROM companies
  WHERE owner_id = $1
  AND is_active = true
) AS is_partner;

-- name: AssignRoleToUser :exec
INSERT INTO user_roles (user_id, role_id)
SELECT $1, r.id FROM roles r WHERE r.name = $2
ON CONFLICT (user_id, role_id) DO NOTHING;

-- name: RemoveRoleFromUser :exec
DELETE FROM user_roles
WHERE user_id = $1 AND role_id = (SELECT id FROM roles WHERE name = $2);

-- name: CheckIfUserHasRole :one
SELECT EXISTS (
  SELECT 1
  FROM user_roles ur
  JOIN roles r ON ur.role_id = r.id
  WHERE ur.user_id = $1 AND r.name = $2
) AS has_role;

-- name: SearchUsers :many
SELECT
  u.external_id,
  u.name,
  u.email,
  u.cpf,
  u.phone_numbers,
  u.cashback_value,
  u.subscription_id,
  u.is_active,
  u.is_deleted,
  u.created_at,
  u.updated_at,
  COUNT(*) OVER() AS total_count
FROM users u
WHERE (
  u.cpf ILIKE '%' || unaccent(sqlc.arg(search_query)::text) || '%' OR
  unaccent(u.name) ILIKE '%' || unaccent(sqlc.arg(search_query)::text) || '%' OR
  unaccent(u.email) ILIKE '%' || unaccent(sqlc.arg(search_query)::text) || '%'
)
ORDER BY u.created_at DESC
LIMIT $1 OFFSET $2;


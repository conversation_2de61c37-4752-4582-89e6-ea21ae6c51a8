-- name: GetRecommendedPriceComparison :many
WITH target_list AS (
    SELECT upl.id AS list_id
    FROM user_products_lists upl
    WHERE upl.external_id = $1
),
all_list_products AS (
    SELECT
        upli.product_id,
        upli.quantity,
        p.external_id AS product_external_id,
        p.name AS product_name
    FROM user_products_lists_items upli
    JOIN target_list tl ON upli.list_id = tl.list_id
    JOIN products p ON p.id = upli.product_id
),
companies_with_addresses AS (
    SELECT
        c.id,
        c.external_id,
        c.name,
        c.picture,
        c.shipping_fee,
        c.delivery_modes,
        ca.latitude,
        ca.longitude
    FROM companies c
    JOIN company_addresses ca ON ca.company_id = c.id AND ca.is_default = TRUE
    WHERE c.is_active = true
),
matched_products AS (
    SELECT
        cp.company_id,
        cp.product_id,
        cp.price,
        cp.discount,
        cp.stock,
        alp.quantity,
        alp.product_external_id
    FROM all_list_products alp
    LEFT JOIN company_products cp ON cp.product_id = alp.product_id
    WHERE cp.stock > 0
),
company_totals AS (
    SELECT
        cwa.external_id AS company_id,
        cwa.name AS company_name,
        cwa.picture AS company_picture,
        cwa.shipping_fee AS company_shipping_fee,
        cwa.delivery_modes AS company_delivery_modes,
        cwa.latitude AS company_latitude,
        cwa.longitude AS company_longitude,
        SUM(mp.price * mp.quantity) AS total_price,
        COUNT(mp.product_id) AS matched_products_count
    FROM matched_products mp
    JOIN companies_with_addresses cwa ON cwa.id = mp.company_id
    GROUP BY cwa.external_id, cwa.name, cwa.picture, cwa.shipping_fee, cwa.delivery_modes, cwa.latitude, cwa.longitude
),
company_matched AS (
    SELECT
        cwa.external_id AS company_id,
        COALESCE(
            jsonb_agg(
                jsonb_build_object(
                    'external_id', p.external_id,
                    'name', p.name,
                    'ean', p.ean,
                    'description', p.description,
                    'image', p.image,
                    'brand', p.brand,
                    'is_18_plus', p.is_18_plus,
                    'price', mp.price,
                    'discount', mp.discount,
                    'stock', mp.stock,
                    'quantity', mp.quantity,
                    'categories', (
                        SELECT json_agg(DISTINCT jsonb_build_object(
                            'name', cat.name,
                            'image', cat.image,
                            'external_id', cat.external_id
                        ))
                        FROM products_categories pc_cat
                        JOIN categories cat ON cat.id = pc_cat.category_id
                        WHERE pc_cat.product_id = p.id
                    )
                ) ORDER BY p.name
            ) FILTER (WHERE p.id IS NOT NULL),
            '[]'::jsonb
        ) AS matched_products
    FROM matched_products mp
    JOIN companies_with_addresses cwa ON cwa.id = mp.company_id
    JOIN products p ON p.id = mp.product_id
    GROUP BY cwa.external_id
),
all_companies AS (
    SELECT DISTINCT cwa.external_id AS company_id, cwa.id AS company_internal_id
    FROM companies_with_addresses cwa
),
product_market_prices AS (
    SELECT
        product_id,
        MIN(price) AS min_price
    FROM company_products cp
    JOIN companies c ON c.id = cp.company_id
    WHERE cp.stock > 0 AND c.is_active = true
    GROUP BY product_id
),
company_unmatched AS (
    SELECT
        ac.company_id,
        COALESCE(
            jsonb_agg(
                jsonb_build_object(
                    'external_id', p.external_id,
                    'name', p.name,
                    'ean', p.ean,
                    'description', p.description,
                    'image', p.image,
                    'brand', p.brand,
                    'is_18_plus', p.is_18_plus,
                    'price', pmp.min_price,
                    'discount', NULL,
                    'stock', 0,
                    'quantity', alp.quantity,
                    'categories', (
                        SELECT json_agg(DISTINCT jsonb_build_object(
                            'name', cat.name,
                            'image', cat.image,
                            'external_id', cat.external_id
                        ))
                        FROM products_categories pc_cat
                        JOIN categories cat ON cat.id = pc_cat.category_id
                        WHERE pc_cat.product_id = p.id
                    ),
                    'substitutes', COALESCE(
                        (
                            SELECT jsonb_agg(
                                jsonb_build_object(
                                    'external_id', p_sub.external_id,
                                    'name', p_sub.name,
                                    'ean', p_sub.ean,
                                    'description', p_sub.description,
                                    'image', p_sub.image,
                                    'brand', p_sub.brand,
                                    'is_18_plus', p_sub.is_18_plus,
                                    'price', cp_sub.price,
                                    'discount', cp_sub.discount,
                                    'stock', cp_sub.stock,
                                    'categories', (
                                        SELECT json_agg(DISTINCT jsonb_build_object(
                                            'name', cat.name,
                                            'image', cat.image,
                                            'external_id', cat.external_id
                                        ))
                                        FROM products_categories pc_cat
                                        JOIN categories cat ON cat.id = pc_cat.category_id
                                        WHERE pc_cat.product_id = p_sub.id
                                    ),
                                    'match_reason', 'same_category'
                                ) ORDER BY cp_sub.price ASC
                            )
                            FROM products p_sub
                            JOIN company_products cp_sub ON cp_sub.product_id = p_sub.id
                            JOIN products_categories pc_sub ON p_sub.id = pc_sub.product_id
                            JOIN products_categories pc_orig ON pc_orig.product_id = alp.product_id
                            WHERE cp_sub.company_id = ac.company_internal_id
                            AND cp_sub.stock > 0
                            AND p_sub.is_active = true
                            AND p_sub.is_reviewed = true
                            AND pc_sub.category_id = pc_orig.category_id
                            AND p_sub.id != alp.product_id
                            LIMIT 3
                        ),
                        '[]'::jsonb
                    )
                ) ORDER BY p.name
            ) FILTER (WHERE alp.product_external_id IS NOT NULL),
            '[]'::jsonb
        ) AS unmatched_products,
        COUNT(alp.product_id) FILTER (WHERE alp.product_external_id IS NOT NULL) AS unmatched_product_count
    FROM all_companies ac
    CROSS JOIN all_list_products alp
    JOIN products p ON p.id = alp.product_id
    LEFT JOIN product_market_prices pmp ON pmp.product_id = p.id
    WHERE NOT EXISTS (
        SELECT 1
        FROM company_products cp_check
        WHERE cp_check.company_id = ac.company_internal_id
        AND cp_check.product_id = alp.product_id
        AND cp_check.stock > 0
    )
    GROUP BY ac.company_id
),
aggregated AS (
    SELECT
        ct.company_id,
        ct.company_name,
        ct.company_picture,
        ct.company_shipping_fee,
        ct.company_delivery_modes,
        ct.company_latitude,
        ct.company_longitude,
        ct.total_price,
        ct.matched_products_count,
        COALESCE(cm.matched_products, '[]'::jsonb) AS matched_products,
        COALESCE(cu.unmatched_products, '[]'::jsonb) AS unmatched_products,
        COALESCE(cu.unmatched_product_count, 0) AS unmatched_product_count
    FROM company_totals ct
    LEFT JOIN company_matched cm ON ct.company_id = cm.company_id
    LEFT JOIN company_unmatched cu ON ct.company_id = cu.company_id
)
SELECT
    company_id,
    company_name,
    company_picture,
    company_shipping_fee,
    company_delivery_modes,
    company_latitude,
    company_longitude,
    total_price,
    matched_products_count,
    matched_products,
    unmatched_products,
    unmatched_product_count
FROM aggregated
ORDER BY total_price ASC, matched_products_count DESC
LIMIT 50;

-- name: GetRecommendedPriceComparisonWithLocation :many
WITH target_list AS (
    SELECT upl.id AS list_id
    FROM user_products_lists upl
    WHERE upl.external_id = $1
),
all_list_products AS (
    SELECT
        upli.product_id,
        upli.quantity,
        p.external_id AS product_external_id,
        p.name AS product_name
    FROM user_products_lists_items upli
    JOIN target_list tl ON upli.list_id = tl.list_id
    JOIN products p ON p.id = upli.product_id
),
companies_with_addresses AS (
    SELECT
        c.id,
        c.external_id,
        c.name,
        c.picture,
        c.shipping_fee,
        c.delivery_modes,
        c.max_shipping_distance_km,
        ca.location,
        ca.latitude,
        ca.longitude
    FROM companies c
    JOIN company_addresses ca ON ca.company_id = c.id AND ca.is_default = TRUE
    WHERE c.is_active = true
      AND ca.location IS NOT NULL
      AND ST_DWithin(ca.location, ST_MakePoint($2, $3)::geography, c.max_shipping_distance_km * 1000)
),
matched_products AS (
    SELECT
        cp.company_id,
        cp.product_id,
        cp.price,
        cp.discount,
        cp.stock,
        alp.quantity,
        alp.product_external_id
    FROM all_list_products alp
    LEFT JOIN company_products cp ON cp.product_id = alp.product_id
    WHERE cp.stock > 0
),
company_totals AS (
    SELECT
        cwa.external_id AS company_id,
        cwa.name AS company_name,
        cwa.picture AS company_picture,
        cwa.shipping_fee AS company_shipping_fee,
        cwa.delivery_modes AS company_delivery_modes,
        cwa.location,
        cwa.latitude AS company_latitude,
        cwa.longitude AS company_longitude,
        SUM(mp.price * mp.quantity) AS total_price,
        COUNT(mp.product_id) AS matched_products_count
    FROM matched_products mp
    JOIN companies_with_addresses cwa ON cwa.id = mp.company_id
    GROUP BY cwa.external_id, cwa.name, cwa.picture, cwa.shipping_fee, cwa.delivery_modes, cwa.location, cwa.latitude, cwa.longitude
),
company_matched AS (
    SELECT
        cwa.external_id AS company_id,
        COALESCE(
            jsonb_agg(
                jsonb_build_object(
                    'external_id', p.external_id,
                    'name', p.name,
                    'ean', p.ean,
                    'description', p.description,
                    'image', p.image,
                    'brand', p.brand,
                    'is_18_plus', p.is_18_plus,
                    'price', mp.price,
                    'discount', mp.discount,
                    'stock', mp.stock,
                    'quantity', mp.quantity,
                    'categories', (
                        SELECT json_agg(DISTINCT jsonb_build_object(
                            'name', cat.name,
                            'image', cat.image,
                            'external_id', cat.external_id
                        ))
                        FROM products_categories pc_cat
                        JOIN categories cat ON cat.id = pc_cat.category_id
                        WHERE pc_cat.product_id = p.id
                    )
                ) ORDER BY p.name
            ) FILTER (WHERE p.id IS NOT NULL),
            '[]'::jsonb
        ) AS matched_products
    FROM matched_products mp
    JOIN companies_with_addresses cwa ON cwa.id = mp.company_id
    JOIN products p ON p.id = mp.product_id
    GROUP BY cwa.external_id
),
all_companies AS (
    SELECT DISTINCT cwa.external_id AS company_id, cwa.id AS company_internal_id
    FROM companies_with_addresses cwa
),
product_market_prices AS (
    SELECT
        product_id,
        MIN(price) AS min_price
    FROM company_products cp
    JOIN companies c ON c.id = cp.company_id
    WHERE cp.stock > 0 AND c.is_active = true
    GROUP BY product_id
),
company_unmatched AS (
    SELECT
        ac.company_id,
        COALESCE(
            jsonb_agg(
                jsonb_build_object(
                    'external_id', p.external_id,
                    'name', p.name,
                    'ean', p.ean,
                    'description', p.description,
                    'image', p.image,
                    'brand', p.brand,
                    'is_18_plus', p.is_18_plus,
                    'price', pmp.min_price,
                    'discount', NULL,
                    'stock', 0,
                    'quantity', alp.quantity,
                    'categories', (
                        SELECT json_agg(DISTINCT jsonb_build_object(
                            'name', cat.name,
                            'image', cat.image,
                            'external_id', cat.external_id
                        ))
                        FROM products_categories pc_cat
                        JOIN categories cat ON cat.id = pc_cat.category_id
                        WHERE pc_cat.product_id = p.id
                    ),
                    'substitutes', COALESCE(
                        (
                            SELECT jsonb_agg(
                                jsonb_build_object(
                                    'external_id', p_sub.external_id,
                                    'name', p_sub.name,
                                    'ean', p_sub.ean,
                                    'description', p_sub.description,
                                    'image', p_sub.image,
                                    'brand', p_sub.brand,
                                    'is_18_plus', p_sub.is_18_plus,
                                    'price', cp_sub.price,
                                    'discount', cp_sub.discount,
                                    'stock', cp_sub.stock,
                                    'categories', (
                                        SELECT json_agg(DISTINCT jsonb_build_object(
                                            'name', cat.name,
                                            'image', cat.image,
                                            'external_id', cat.external_id
                                        ))
                                        FROM products_categories pc_cat
                                        JOIN categories cat ON cat.id = pc_cat.category_id
                                        WHERE pc_cat.product_id = p_sub.id
                                    ),
                                    'match_reason', 'same_category'
                                ) ORDER BY cp_sub.price ASC
                            )
                            FROM products p_sub
                            JOIN company_products cp_sub ON cp_sub.product_id = p_sub.id
                            JOIN products_categories pc_sub ON p_sub.id = pc_sub.product_id
                            JOIN products_categories pc_orig ON pc_orig.product_id = alp.product_id
                            WHERE cp_sub.company_id = ac.company_internal_id
                            AND cp_sub.stock > 0
                            AND p_sub.is_active = true
                            AND p_sub.is_reviewed = true
                            AND pc_sub.category_id = pc_orig.category_id
                            AND p_sub.id != alp.product_id
                            LIMIT 3
                        ),
                        '[]'::jsonb
                    )
                ) ORDER BY p.name
            ) FILTER (WHERE alp.product_external_id IS NOT NULL),
            '[]'::jsonb
        ) AS unmatched_products,
        COUNT(alp.product_id) FILTER (WHERE alp.product_external_id IS NOT NULL) AS unmatched_product_count
    FROM all_companies ac
    CROSS JOIN all_list_products alp
    JOIN products p ON p.id = alp.product_id
    LEFT JOIN product_market_prices pmp ON pmp.product_id = p.id
    WHERE NOT EXISTS (
        SELECT 1
        FROM company_products cp_check
        WHERE cp_check.company_id = ac.company_internal_id
        AND cp_check.product_id = alp.product_id
        AND cp_check.stock > 0
    )
    GROUP BY ac.company_id
),
aggregated AS (
    SELECT
        ct.company_id,
        ct.company_name,
        ct.company_picture,
        ct.company_shipping_fee,
        ct.company_delivery_modes,
        ct.location,
        ct.company_latitude,
        ct.company_longitude,
        ct.total_price,
        ct.matched_products_count,
        COALESCE(cm.matched_products, '[]'::jsonb) AS matched_products,
        COALESCE(cu.unmatched_products, '[]'::jsonb) AS unmatched_products,
        COALESCE(cu.unmatched_product_count, 0) AS unmatched_product_count
    FROM company_totals ct
    LEFT JOIN company_matched cm ON ct.company_id = cm.company_id
    LEFT JOIN company_unmatched cu ON ct.company_id = cu.company_id
)
SELECT
    company_id,
    company_name,
    company_picture,
    company_shipping_fee,
    company_delivery_modes,
    company_latitude,
    company_longitude,
    total_price,
    matched_products_count,
    matched_products,
    unmatched_products,
    unmatched_product_count,
    ROUND(
        (ST_DistanceSphere(location, ST_MakePoint($2, $3)) / 1000.0)::numeric, 2
    ) AS distance_km
FROM aggregated
ORDER BY total_price ASC, distance_km ASC, matched_products_count DESC
LIMIT 50;

-- name: GetDefaultPriceComparison :many
WITH target_list AS (
    SELECT upl.id AS list_id
    FROM user_products_lists upl
    WHERE upl.external_id = $1
),
all_list_products AS (
    SELECT
        upli.product_id,
        upli.quantity,
        p.external_id AS product_external_id,
        p.name AS product_name
    FROM user_products_lists_items upli
    JOIN target_list tl ON upli.list_id = tl.list_id
    JOIN products p ON p.id = upli.product_id
),
companies_with_addresses AS (
    SELECT
        c.id,
        c.external_id,
        c.name,
        c.picture,
        c.shipping_fee,
        c.delivery_modes,
        c.rating,
        ca.location,
        ca.latitude,
        ca.longitude
    FROM companies c
    JOIN company_addresses ca ON ca.company_id = c.id AND ca.is_default = TRUE
    WHERE c.is_active = true
),
matched_products AS (
    SELECT
        cp.company_id,
        cp.product_id,
        cp.price,
        cp.discount,
        cp.stock,
        alp.quantity,
        alp.product_external_id
    FROM all_list_products alp
    LEFT JOIN company_products cp ON cp.product_id = alp.product_id
    WHERE cp.stock > 0
),
company_totals AS (
    SELECT
        cwa.id AS company_id,
        cwa.external_id AS company_external_id,
        cwa.name AS company_name,
        cwa.picture AS company_picture,
        cwa.shipping_fee AS company_shipping_fee,
        cwa.delivery_modes AS company_delivery_modes,
        cwa.location,
        cwa.latitude AS company_latitude,
        cwa.longitude AS company_longitude,
        cwa.rating,
        SUM(mp.price * mp.quantity) AS total_price,
        COUNT(mp.product_id) AS matched_products_count
    FROM matched_products mp
    JOIN companies_with_addresses cwa ON cwa.id = mp.company_id
    GROUP BY cwa.id, cwa.external_id, cwa.name, cwa.picture, cwa.shipping_fee, cwa.delivery_modes, cwa.location, cwa.latitude, cwa.longitude, cwa.rating
),
company_matched AS (
    SELECT
        cwa.id AS company_id,
        COALESCE(
            jsonb_agg(
                jsonb_build_object(
                    'external_id', p.external_id,
                    'name', p.name,
                    'ean', p.ean,
                    'description', p.description,
                    'image', p.image,
                    'brand', p.brand,
                    'is_18_plus', p.is_18_plus,
                    'price', mp.price,
                    'discount', mp.discount,
                    'stock', mp.stock,
                    'quantity', mp.quantity,
                    'categories', (
                        SELECT json_agg(DISTINCT jsonb_build_object(
                            'name', cat.name,
                            'image', cat.image,
                            'external_id', cat.external_id
                        ))
                        FROM products_categories pc_cat
                        JOIN categories cat ON cat.id = pc_cat.category_id
                        WHERE pc_cat.product_id = p.id
                    )
                ) ORDER BY p.name
            ) FILTER (WHERE p.id IS NOT NULL),
            '[]'::jsonb
        ) AS matched_products
    FROM matched_products mp
    JOIN companies_with_addresses cwa ON cwa.id = mp.company_id
    JOIN products p ON p.id = mp.product_id
    GROUP BY cwa.id
),
all_companies AS (
    SELECT DISTINCT cwa.id AS company_id
    FROM companies_with_addresses cwa
),
product_market_prices_default AS (
    SELECT
        product_id,
        MIN(price) AS min_price
    FROM company_products cp
    JOIN companies c ON c.id = cp.company_id
    WHERE cp.stock > 0 AND c.is_active = true
    GROUP BY product_id
),
company_unmatched AS (
    SELECT
        ac.company_id,
        COALESCE(
            jsonb_agg(
                jsonb_build_object(
                    'external_id', p.external_id,
                    'name', p.name,
                    'ean', p.ean,
                    'description', p.description,
                    'image', p.image,
                    'brand', p.brand,
                    'is_18_plus', p.is_18_plus,
                    'price', pmp.min_price,
                    'discount', NULL,
                    'stock', 0,
                    'quantity', alp.quantity,
                    'categories', (
                        SELECT json_agg(DISTINCT jsonb_build_object(
                            'name', cat.name,
                            'image', cat.image,
                            'external_id', cat.external_id
                        ))
                        FROM products_categories pc_cat
                        JOIN categories cat ON cat.id = pc_cat.category_id
                        WHERE pc_cat.product_id = p.id
                    ),
                    'substitutes', COALESCE(
                        (
                            SELECT jsonb_agg(
                                jsonb_build_object(
                                    'external_id', p_sub.external_id,
                                    'name', p_sub.name,
                                    'ean', p_sub.ean,
                                    'description', p_sub.description,
                                    'image', p_sub.image,
                                    'brand', p_sub.brand,
                                    'is_18_plus', p_sub.is_18_plus,
                                    'price', cp_sub.price,
                                    'discount', cp_sub.discount,
                                    'stock', cp_sub.stock,
                                    'categories', (
                                        SELECT json_agg(DISTINCT jsonb_build_object(
                                            'name', cat.name,
                                            'image', cat.image,
                                            'external_id', cat.external_id
                                        ))
                                        FROM products_categories pc_cat
                                        JOIN categories cat ON cat.id = pc_cat.category_id
                                        WHERE pc_cat.product_id = p_sub.id
                                    ),
                                    'match_reason', 'same_category'
                                ) ORDER BY cp_sub.price ASC
                            )
                            FROM products p_sub
                            JOIN company_products cp_sub ON cp_sub.product_id = p_sub.id
                            JOIN products_categories pc_sub ON p_sub.id = pc_sub.product_id
                            JOIN products_categories pc_orig ON pc_orig.product_id = alp.product_id
                            WHERE cp_sub.company_id = ac.company_id
                            AND cp_sub.stock > 0
                            AND p_sub.is_active = true
                            AND p_sub.is_reviewed = true
                            AND pc_sub.category_id = pc_orig.category_id
                            AND p_sub.id != alp.product_id
                            LIMIT 3
                        ),
                        '[]'::jsonb
                    )
                ) ORDER BY p.name
            ) FILTER (WHERE alp.product_external_id IS NOT NULL),
            '[]'::jsonb
        ) AS unmatched_products,
        COUNT(alp.product_id) FILTER (WHERE alp.product_external_id IS NOT NULL) AS unmatched_product_count
    FROM all_companies ac
    CROSS JOIN all_list_products alp
    JOIN products p ON p.id = alp.product_id
    LEFT JOIN product_market_prices_default pmp ON pmp.product_id = p.id
    WHERE NOT EXISTS (
        SELECT 1
        FROM company_products cp_check
        WHERE cp_check.company_id = ac.company_id
        AND cp_check.product_id = alp.product_id
        AND cp_check.stock > 0
    )
    GROUP BY ac.company_id
),
aggregated AS (
    SELECT
        ct.company_id,
        ct.company_external_id,
        ct.company_name,
        ct.company_picture,
        ct.company_shipping_fee,
        ct.company_delivery_modes,
        ct.location,
        ct.company_latitude,
        ct.company_longitude,
        ct.rating,
        ct.total_price,
        ct.matched_products_count,
        COALESCE(cm.matched_products, '[]'::jsonb) AS matched_products,
        COALESCE(cu.unmatched_products, '[]'::jsonb) AS unmatched_products,
        COALESCE(cu.unmatched_product_count, 0) AS unmatched_product_count
    FROM company_totals ct
    LEFT JOIN company_matched cm ON ct.company_id = cm.company_id
    LEFT JOIN company_unmatched cu ON ct.company_id = cu.company_id
)
SELECT
    a.company_external_id AS company_external_id,
    a.company_name,
    a.company_picture,
    a.company_shipping_fee,
    a.company_delivery_modes,
    a.company_latitude,
    a.company_longitude,
    a.total_price,
    a.matched_products_count,
    a.matched_products,
    a.unmatched_products,
    a.unmatched_product_count,
    ROUND(
    (ST_Distance(a.location::geography, ST_MakePoint($2::float8, $3::float8)::geography) / 1000.0)::numeric, 2) AS distance_km,
    a.rating
FROM aggregated a
ORDER BY total_price ASC, distance_km ASC, rating DESC
LIMIT 50;

-- name: GetDefaultPriceComparisonWithLocation :many
WITH target_list AS (
    SELECT upl.id AS list_id
    FROM user_products_lists upl
    WHERE upl.external_id = $1
),
all_list_products AS (
    SELECT
        upli.product_id,
        upli.quantity,
        p.external_id AS product_external_id,
        p.name AS product_name
    FROM user_products_lists_items upli
    JOIN target_list tl ON upli.list_id = tl.list_id
    JOIN products p ON p.id = upli.product_id
),
companies_with_addresses AS (
    SELECT
        c.id,
        c.external_id,
        c.name,
        c.picture,
        c.shipping_fee,
        c.delivery_modes,
        c.rating,
        c.max_shipping_distance_km,
        ca.location,
        ca.latitude,
        ca.longitude
    FROM companies c
    JOIN company_addresses ca ON ca.company_id = c.id AND ca.is_default = TRUE
    WHERE c.is_active = true
      AND ca.location IS NOT NULL
      AND ST_DWithin(ca.location, ST_MakePoint($2, $3)::geography, c.max_shipping_distance_km * 1000)
),
matched_products AS (
    SELECT
        cp.company_id,
        cp.product_id,
        cp.price,
        cp.discount,
        cp.stock,
        alp.quantity,
        alp.product_external_id
    FROM all_list_products alp
    LEFT JOIN company_products cp ON cp.product_id = alp.product_id
    WHERE cp.stock > 0
),
company_totals AS (
    SELECT
        cwa.id AS company_id,
        cwa.external_id AS company_external_id,
        cwa.name AS company_name,
        cwa.picture AS company_picture,
        cwa.shipping_fee AS company_shipping_fee,
        cwa.delivery_modes AS company_delivery_modes,
        cwa.location,
        cwa.latitude AS company_latitude,
        cwa.longitude AS company_longitude,
        cwa.rating,
        SUM(mp.price * mp.quantity) AS total_price,
        COUNT(mp.product_id) AS matched_products_count
    FROM matched_products mp
    JOIN companies_with_addresses cwa ON cwa.id = mp.company_id
    GROUP BY cwa.id, cwa.external_id, cwa.name, cwa.picture, cwa.shipping_fee, cwa.delivery_modes, cwa.location, cwa.latitude, cwa.longitude, cwa.rating
),
company_matched AS (
    SELECT
        cwa.id AS company_id,
        COALESCE(
            jsonb_agg(
                jsonb_build_object(
                    'external_id', p.external_id,
                    'name', p.name,
                    'ean', p.ean,
                    'description', p.description,
                    'image', p.image,
                    'brand', p.brand,
                    'is_18_plus', p.is_18_plus,
                    'price', mp.price,
                    'discount', mp.discount,
                    'stock', mp.stock,
                    'quantity', mp.quantity,
                    'categories', (
                        SELECT json_agg(DISTINCT jsonb_build_object(
                            'name', cat.name,
                            'image', cat.image,
                            'external_id', cat.external_id
                        ))
                        FROM products_categories pc_cat
                        JOIN categories cat ON cat.id = pc_cat.category_id
                        WHERE pc_cat.product_id = p.id
                    )
                ) ORDER BY p.name
            ) FILTER (WHERE p.id IS NOT NULL),
            '[]'::jsonb
        ) AS matched_products
    FROM matched_products mp
    JOIN companies_with_addresses cwa ON cwa.id = mp.company_id
    JOIN products p ON p.id = mp.product_id
    GROUP BY cwa.id
),
all_companies AS (
    SELECT DISTINCT cwa.id AS company_id
    FROM companies_with_addresses cwa
),
product_market_prices_default AS (
    SELECT
        product_id,
        MIN(price) AS min_price
    FROM company_products cp
    JOIN companies c ON c.id = cp.company_id
    WHERE cp.stock > 0 AND c.is_active = true
    GROUP BY product_id
),
company_unmatched AS (
    SELECT
        ac.company_id,
        COALESCE(
            jsonb_agg(
                jsonb_build_object(
                    'external_id', p.external_id,
                    'name', p.name,
                    'ean', p.ean,
                    'description', p.description,
                    'image', p.image,
                    'brand', p.brand,
                    'is_18_plus', p.is_18_plus,
                    'price', pmp.min_price,
                    'discount', NULL,
                    'stock', 0,
                    'quantity', alp.quantity,
                    'categories', (
                        SELECT json_agg(DISTINCT jsonb_build_object(
                            'name', cat.name,
                            'image', cat.image,
                            'external_id', cat.external_id
                        ))
                        FROM products_categories pc_cat
                        JOIN categories cat ON cat.id = pc_cat.category_id
                        WHERE pc_cat.product_id = p.id
                    ),
                    'substitutes', COALESCE(
                        (
                            SELECT jsonb_agg(
                                jsonb_build_object(
                                    'external_id', p_sub.external_id,
                                    'name', p_sub.name,
                                    'ean', p_sub.ean,
                                    'description', p_sub.description,
                                    'image', p_sub.image,
                                    'brand', p_sub.brand,
                                    'is_18_plus', p_sub.is_18_plus,
                                    'price', cp_sub.price,
                                    'discount', cp_sub.discount,
                                    'stock', cp_sub.stock,
                                    'categories', (
                                        SELECT json_agg(DISTINCT jsonb_build_object(
                                            'name', cat.name,
                                            'image', cat.image,
                                            'external_id', cat.external_id
                                        ))
                                        FROM products_categories pc_cat
                                        JOIN categories cat ON cat.id = pc_cat.category_id
                                        WHERE pc_cat.product_id = p_sub.id
                                    ),
                                    'match_reason', 'same_category'
                                ) ORDER BY cp_sub.price ASC
                            )
                            FROM products p_sub
                            JOIN company_products cp_sub ON cp_sub.product_id = p_sub.id
                            JOIN products_categories pc_sub ON p_sub.id = pc_sub.product_id
                            JOIN products_categories pc_orig ON pc_orig.product_id = alp.product_id
                            WHERE cp_sub.company_id = ac.company_id
                            AND cp_sub.stock > 0
                            AND p_sub.is_active = true
                            AND p_sub.is_reviewed = true
                            AND pc_sub.category_id = pc_orig.category_id
                            AND p_sub.id != alp.product_id
                            LIMIT 3
                        ),
                        '[]'::jsonb
                    )
                ) ORDER BY p.name
            ) FILTER (WHERE alp.product_external_id IS NOT NULL),
            '[]'::jsonb
        ) AS unmatched_products,
        COUNT(alp.product_id) FILTER (WHERE alp.product_external_id IS NOT NULL) AS unmatched_product_count
    FROM all_companies ac
    CROSS JOIN all_list_products alp
    JOIN products p ON p.id = alp.product_id
    LEFT JOIN product_market_prices_default pmp ON pmp.product_id = p.id
    WHERE NOT EXISTS (
        SELECT 1
        FROM company_products cp_check
        WHERE cp_check.company_id = ac.company_id
        AND cp_check.product_id = alp.product_id
        AND cp_check.stock > 0
    )
    GROUP BY ac.company_id
),
aggregated AS (
    SELECT
        ct.company_id,
        ct.company_external_id,
        ct.company_name,
        ct.company_picture,
        ct.company_shipping_fee,
        ct.company_delivery_modes,
        ct.location,
        ct.company_latitude,
        ct.company_longitude,
        ct.rating,
        ct.total_price,
        ct.matched_products_count,
        COALESCE(cm.matched_products, '[]'::jsonb) AS matched_products,
        COALESCE(cu.unmatched_products, '[]'::jsonb) AS unmatched_products,
        COALESCE(cu.unmatched_product_count, 0) AS unmatched_product_count
    FROM company_totals ct
    LEFT JOIN company_matched cm ON ct.company_id = cm.company_id
    LEFT JOIN company_unmatched cu ON ct.company_id = cu.company_id
)
SELECT
    a.company_external_id AS company_external_id,
    a.company_name,
    a.company_picture,
    a.company_shipping_fee,
    a.company_delivery_modes,
    a.company_latitude,
    a.company_longitude,
    a.total_price,
    a.matched_products_count,
    a.matched_products,
    a.unmatched_products,
    a.unmatched_product_count,
    ROUND(
        (ST_DistanceSphere(a.location, ST_MakePoint($2, $3)) / 1000.0)::numeric, 2
    ) AS distance_km,
    a.rating
FROM aggregated a
ORDER BY total_price ASC, distance_km ASC, rating DESC
LIMIT 50;

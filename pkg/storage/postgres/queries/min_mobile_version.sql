-- name: GetMinMobileVersion :one
SELECT ios_version, android_version, is_active FROM min_mobile_version ORDER BY created_at DESC LIMIT 1;

-- name: UpsertMinMobileVersion :exec
INSERT INTO min_mobile_version (id, ios_version, android_version)
VALUES (1, $1, $2)
ON CONFLICT (id) DO UPDATE
SET ios_version = EXCLUDED.ios_version,
    android_version = EXCLUDED.android_version,
    updated_at = NOW();

-- name: HandleMinMobileVersionActivation :exec
UPDATE min_mobile_version SET is_active = $1 WHERE id = 1;

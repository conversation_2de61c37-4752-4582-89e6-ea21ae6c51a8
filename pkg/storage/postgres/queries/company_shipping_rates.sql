-- name: CreateCompanyShippingRate :one
INSERT INTO company_shipping_rates (
    company_id, distance_min_km, distance_max_km, fee_centavos, external_id
) VALUES (
    $1, $2, $3, $4, $5
) RETURNING id;

-- name: GetCompanyShippingRates :many
SELECT 
    id,
    company_id,
    distance_min_km,
    distance_max_km,
    fee_centavos,
    external_id,
    created_at,
    updated_at
FROM company_shipping_rates
WHERE company_id = $1
ORDER BY distance_min_km ASC;

-- name: GetCompanyShippingRateByExternalID :one
SELECT 
    id,
    company_id,
    distance_min_km,
    distance_max_km,
    fee_centavos,
    external_id,
    created_at,
    updated_at
FROM company_shipping_rates
WHERE external_id = $1;

-- name: UpdateCompanyShippingRate :exec
UPDATE company_shipping_rates
SET 
    distance_min_km = $2,
    distance_max_km = $3,
    fee_centavos = $4,
    updated_at = NOW()
WHERE external_id = $1;

-- name: DeleteCompanyShippingRate :exec
DELETE FROM company_shipping_rates
WHERE external_id = $1;

-- name: GetShippingFeeForDistance :one
SELECT fee_centavos
FROM company_shipping_rates
WHERE company_id = $1
  AND $2 >= distance_min_km
  AND $2 < distance_max_km
LIMIT 1;

-- name: CalculateShippingFeeWithDistance :one
WITH company_location AS (
    SELECT 
        c.id as company_id,
        ca.latitude,
        ca.longitude,
        ca.location
    FROM companies c
    JOIN company_addresses ca ON c.id = ca.company_id AND ca.is_default = true
    WHERE sqlc.arg(company_external_id) = c.external_id
),
user_location AS (
    SELECT 
        ua.latitude,
        ua.longitude,
        ua.location
    FROM user_addresses ua
    WHERE sqlc.arg(user_address_external_id) = ua.external_id
),
distance_calc AS (
    SELECT 
        cl.company_id,
        ROUND(
            (ST_Distance(cl.location::geography, ul.location::geography) / 1000.0)::numeric, 2
        ) AS distance_km
    FROM company_location cl
    CROSS JOIN user_location ul
)
SELECT 
    dc.distance_km,
    COALESCE(csr.fee_centavos, 0) as shipping_fee_centavos
FROM distance_calc dc
LEFT JOIN company_shipping_rates csr ON csr.company_id = dc.company_id
    AND dc.distance_km >= csr.distance_min_km
    AND dc.distance_km < csr.distance_max_km
LIMIT 1;

-- name: GetCompanyShippingRatesWithCompanyInfo :many
SELECT 
    csr.id,
    csr.company_id,
    csr.distance_min_km,
    csr.distance_max_km,
    csr.fee_centavos,
    csr.external_id,
    csr.created_at,
    csr.updated_at,
    c.name as company_name,
    c.external_id as company_external_id
FROM company_shipping_rates csr
JOIN companies c ON csr.company_id = c.id
WHERE c.owner_id = $1
ORDER BY c.name ASC, csr.distance_min_km ASC;

-- name: DeleteAllCompanyShippingRates :exec
DELETE FROM company_shipping_rates
WHERE company_id = $1;

-- name: CalculateShippingFeeWithCoordinates :one
WITH company_location AS (
    SELECT
        c.id as company_id,
        ca.location
    FROM companies c
    JOIN company_addresses ca ON c.id = ca.company_id
    WHERE sqlc.arg(company_external_id) = c.external_id
    LIMIT 1
),
distance_calc AS (
    SELECT
        cl.company_id,
        ROUND(
            (ST_Distance(
                cl.location::geography,
                ST_Point(sqlc.arg(user_longitude), sqlc.arg(user_latitude))::geography
            ) / 1000.0)::numeric, 2
        ) AS distance_km
    FROM company_location cl
)
SELECT
    dc.distance_km,
    COALESCE(csr.fee_centavos, 0) as shipping_fee_centavos
FROM distance_calc dc
LEFT JOIN company_shipping_rates csr ON csr.company_id = dc.company_id
    AND dc.distance_km >= csr.distance_min_km
    AND dc.distance_km < csr.distance_max_km
LIMIT 1;

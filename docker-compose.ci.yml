version: '3.8'

services:
  # PostgreSQL database with PostGIS for testing
  test-db:
    image: postgis/postgis:17-3.5
    environment:
      POSTGRES_DB: izymercado_test
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5434:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    volumes:
      - test_db_data:/var/lib/postgresql/data

  # Backend API service built from Git repository
  test-api:
    build:
      context: .
      dockerfile: Dockerfile.git
      args:
        GIT_REPO: ${BACKEND_REPO_URL:-https://github.com/izy-mercado/backend.git}
        GIT_BRANCH: ${BACKEND_BRANCH:-main}
    environment:
      # Database configuration
      DATABASE_URL: *****************************************/izymercado_test

      # Application configuration
      PORT: 8080
      APP_ENV: test
      GO_ENV: test
      NODE_ENV: test

      # Authentication & Security (using GitHub Secrets in CI)
      JWT_SECRET: ${JWT_SECRET:-topsecret}
      BASIC_AUTH_PASSWORD: ${BASIC_AUTH_PASSWORD:-topsecret}

      # API Configuration
      SWAGGER_HOST: localhost:8081

      # Storage Configuration (Cloudflare R2) - using GitHub Secrets
      CLOUDFLARE_R2_ACCOUNT_ID: ${CLOUDFLARE_R2_ACCOUNT_ID:-}
      CLOUDFLARE_R2_ACCESS_KEY_ID: ${CLOUDFLARE_R2_ACCESS_KEY_ID:-}
      CLOUDFLARE_R2_SECRET_ACCESS_KEY: ${CLOUDFLARE_R2_SECRET_ACCESS_KEY:-}
      CLOUDFLARE_R2_BUCKET_NAME: ${CLOUDFLARE_R2_BUCKET_NAME:-}
      CLOUDFLARE_R2_PUBLIC_URL: ${CLOUDFLARE_R2_PUBLIC_URL:-}

      # External APIs - using GitHub Secrets
      MAPBOX_ACCESS_TOKEN: ${MAPBOX_ACCESS_TOKEN:-}
      VIACEP_API_URL: ${VIACEP_API_URL:-https://viacep.com.br/ws}

      # Firebase Configuration - using GitHub Secrets
      FIREBASE_PROJECT_ID: ${FIREBASE_PROJECT_ID:-}
      FIREBASE_PRIVATE_KEY_ID: ${FIREBASE_PRIVATE_KEY_ID:-}
      FIREBASE_PRIVATE_KEY: ${FIREBASE_PRIVATE_KEY:-}
      FIREBASE_CLIENT_EMAIL: ${FIREBASE_CLIENT_EMAIL:-}
      FIREBASE_CLIENT_ID: ${FIREBASE_CLIENT_ID:-}
      FIREBASE_AUTH_URI: ${FIREBASE_AUTH_URI:-}
      FIREBASE_TOKEN_URI: ${FIREBASE_TOKEN_URI:-}
      FIREBASE_AUTH_PROVIDER_X509_CERT_URL: ${FIREBASE_AUTH_PROVIDER_X509_CERT_URL:-}
      FIREBASE_CLIENT_X509_CERT_URL: ${FIREBASE_CLIENT_X509_CERT_URL:-}

      # Email Configuration - using GitHub Secrets
      SMTP_HOST: ${SMTP_HOST:-}
      SMTP_PORT: ${SMTP_PORT:-587}
      SMTP_USERNAME: ${SMTP_USERNAME:-}
      SMTP_PASSWORD: ${SMTP_PASSWORD:-}
      SMTP_FROM_EMAIL: ${SMTP_FROM_EMAIL:-}
      SMTP_FROM_NAME: ${SMTP_FROM_NAME:-}

      # Payment Configuration - using GitHub Secrets
      STRIPE_SECRET_KEY: ${STRIPE_SECRET_KEY:-}
      STRIPE_WEBHOOK_SECRET: ${STRIPE_WEBHOOK_SECRET:-}

      # WhatsApp Configuration - using GitHub Secrets
      WHATSAPP_API_URL: ${WHATSAPP_API_URL:-}
      WHATSAPP_API_TOKEN: ${WHATSAPP_API_TOKEN:-}
    ports:
      - "8081:8080"
    depends_on:
      test-db:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "-H", "Accept: application/json", "http://localhost:8080/health"]
      interval: 15s
      timeout: 10s
      retries: 12
      start_period: 60s  # Give more time for Git clone + build

  # Mock API service (fallback)
  mock-api:
    image: node:18-alpine
    working_dir: /app
    volumes:
      - ./e2e/mock-api:/app
    command: sh -c "npm install && npm start"
    environment:
      PORT: 8080
    ports:
      - "8082:8080"
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 15s

volumes:
  test_db_data:

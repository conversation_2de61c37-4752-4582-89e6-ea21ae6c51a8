# 📹 E2E Testing & Video Recording

Complete guide for End-to-End testing with automatic video recording in the Painel Parceiro project.

## 🎬 Overview

This project includes a comprehensive E2E testing suite with automatic video recording. The system provides:

### **🧪 Test Coverage**
- **27 passing tests** covering all core functionality
- **Admin dashboard management** (companies, products, users)
- **Frontend application testing** (loading, responsive design)
- **Backend integration** via Testcontainers
- **Complete application demo** in a single video

### **📹 Video Recording**
- **Automatic recording** of all test executions
- **Debug test failures** by watching exactly what happened
- **Document functionality** for stakeholders
- **Create demos** of application features
- **Visual verification** of UI interactions

## 🚀 Quick Start

### **1. Run E2E Tests**

```bash
# Run all tests (videos recorded automatically)
npm run test:e2e

# Run complete application demo (single video)
npm run test:e2e:demo

# Run specific test pattern
npm run test:e2e -- --grep "should create new company"

# Run with specific browser
npm run test:e2e -- --project=chromium
```

### **2. View Test Videos**

```bash
# Interactive video viewer (recommended)
npm run test:e2e:view-videos

# List all available videos
npm run test:e2e:videos

# Direct script access
./scripts/view-test-videos.sh
```

### **3. Available Test Suites**

| Command | Description | Tests | Video Output |
|---------|-------------|-------|--------------|
| `npm run test:e2e` | All tests | 27 tests | Individual videos per test |
| `npm run test:e2e:demo` | Complete demo | 1 comprehensive test | Single 30-second demo video |
| `npm run test:e2e:ui` | Interactive mode | Visual test runner | Videos on demand |

## 📁 Video Storage

Videos are stored in the `test-results/` directory with the following structure:

```
test-results/
├── tests-admin-dashboard-Admin-xxxxx-create-new-company-chromium/
│   ├── video.webm
│   └── trace.zip (if available)
├── tests-basic-frontend-Basic-xxxxx-load-application-chromium/
│   ├── video.webm
│   └── screenshot.png (if test failed)
└── ...
```

### Video File Naming Convention

- **Format**: `tests-{test-file}-{test-name}-{browser}/video.webm`
- **Example**: `tests-admin-dashboard-Admin-Dashboard-should-create-new-company-chromium/video.webm`

## 🎥 Video Configuration

### Current Settings

- **Format**: WebM (widely supported, good compression)
- **Recording**: All tests (passing and failing)
- **Quality**: Standard (optimized for file size)
- **Location**: `test-results/` directory

### Customization Options

You can modify video recording behavior in `playwright.config.ts`:

```typescript
use: {
  // Record videos for all tests
  video: 'on',
  
  // Alternative options:
  // video: 'off',                    // No videos
  // video: 'retain-on-failure',      // Only failed tests
  // video: 'on-first-retry',         // Only on retries
}
```

## 🛠️ Available Scripts

| Script | Description |
|--------|-------------|
| `npm run test:e2e` | Run all E2E tests with video recording |
| `npm run test:e2e:videos` | List all recorded videos |
| `npm run test:e2e:view-videos` | Interactive video viewer |
| `./scripts/view-test-videos.sh` | Direct script access |

## 📱 Video Viewer Features

The interactive video viewer (`npm run test:e2e:view-videos`) provides:

1. **List all videos** with file sizes and timestamps
2. **Open specific video** by number selection
3. **Open all videos** at once
4. **Browse test-results folder** in file manager
5. **Cross-platform support** (Linux, macOS, Windows)

### Supported Video Players

The script automatically detects and uses:
- **Linux**: `xdg-open` (default system player)
- **macOS**: `open` (default system player)
- **Cross-platform**: VLC Media Player
- **Fallback**: Shows file path for manual opening

## 🎯 Use Cases

### 1. Debugging Failed Tests

When a test fails, the video shows exactly what happened:

```bash
# Run failing test
npm run test:e2e -- --grep "problematic test"

# View the video to see the failure
npm run test:e2e:view-videos
```

### 2. Creating Documentation

Use videos to document application workflows:

```bash
# Record specific user journey
npm run test:e2e -- --grep "user registration flow"

# Share video with stakeholders
npm run test:e2e:view-videos
```

### 3. Regression Testing

Compare videos before and after changes:

```bash
# Before changes
npm run test:e2e
cp -r test-results test-results-before

# After changes
npm run test:e2e
# Compare videos manually
```

## 🔧 Troubleshooting

### No Videos Generated

1. **Check configuration**: Ensure `video: 'on'` in `playwright.config.ts`
2. **Run tests**: Videos are only created when tests actually run
3. **Check permissions**: Ensure write access to `test-results/` directory

### Videos Won't Open

1. **Install video player**: Ensure you have a WebM-compatible player
2. **Try VLC**: Download VLC Media Player for universal support
3. **Browser fallback**: Open `.webm` files directly in Chrome/Firefox

### Large File Sizes

Videos can be large. To manage storage:

```bash
# Clean old videos
rm -rf test-results/

# Or keep only recent videos
find test-results -name "*.webm" -mtime +7 -delete
```

## 📊 Best Practices

1. **Regular Cleanup**: Remove old videos to save disk space
2. **Selective Recording**: Use `retain-on-failure` for CI/CD to reduce storage
3. **Share Videos**: Use videos for bug reports and feature demonstrations
4. **Archive Important Videos**: Save videos of critical test scenarios

## 🚀 Advanced Usage

### Custom Video Settings

For specific needs, you can override video settings per test:

```typescript
test('important test', async ({ page }) => {
  // This test will have custom video settings
  await page.video()?.path(); // Get video path
  // ... test code
});
```

### Integration with CI/CD

For continuous integration, consider:

```typescript
// In playwright.config.ts
video: process.env.CI ? 'retain-on-failure' : 'on'
```

This records all videos locally but only failures in CI.

---

## 📞 Support

If you encounter issues with video recording:

1. Check the [Playwright Video Documentation](https://playwright.dev/docs/videos)
2. Verify your system has video codecs installed
3. Try different video players if playback fails
4. Check file permissions in the `test-results/` directory

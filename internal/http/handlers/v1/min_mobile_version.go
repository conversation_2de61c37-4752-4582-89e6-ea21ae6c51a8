package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"regexp"

	"github.com/go-chi/chi/v5"
	"github.com/izy-mercado/backend/internal/config"
	"github.com/izy-mercado/backend/internal/http/common"
	"github.com/izy-mercado/backend/internal/logger"
	"github.com/izy-mercado/backend/internal/middlewares"
	"github.com/izy-mercado/backend/pkg/storage/postgres"
)

type MinMobileVersionHandler struct {
	queries *postgres.Queries
	env     *config.Environment
	logger  logger.Logger
}

func NewMinMobileVersionHandler(env *config.Environment, queries *postgres.Queries, log logger.Logger) *chi.Mux {
	h := &MinMobileVersionHandler{
		queries: queries,
		env:     env,
		logger:  log,
	}
	m := middlewares.New(env, queries)
	router := chi.NewRouter()
	router.Get("/", h.GetMinMobileVersion)

	adminRouter := router.With(m.AdminPermissions)
	adminRouter.Post("/", h.UpsertMinMobileVersion)
	adminRouter.Post("/activation/{is_active}", h.HandleMinMobileVersionActivation)
	return router
}

type MinMobileVersionPayload struct {
	Ios     string `json:"ios"`
	Android string `json:"android"`
}

type MinMobileVersionResponse struct {
	Ios      string `json:"ios"`
	Android  string `json:"android"`
	IsActive bool   `json:"is_active"`
}

// GetMinMobileVersion godoc
// @Summary Get minimum mobile version
// @Description Get minimum mobile version
// @Tags MinMobileVersion
// @Accept json
// @Produce json
// @Success 200 {object} MinMobileVersionResponse "Minimum mobile version"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /v1/min-mobile-version [get]
func (h *MinMobileVersionHandler) GetMinMobileVersion(w http.ResponseWriter, r *http.Request) {
	row, err := h.queries.GetMinMobileVersion(r.Context())
	if err != nil {
		h.logger.WithError(err).Error("Failed to get min mobile version")
		common.RespondError(w, err, http.StatusInternalServerError)
		return
	}

	if !row.IsActive {
		common.RespondSuccess(w, MinMobileVersionResponse{}, http.StatusInternalServerError)
		return
	}

	response := MinMobileVersionResponse{
		Ios:      row.IosVersion,
		Android:  row.AndroidVersion,
		IsActive: row.IsActive,
	}
	common.RespondSuccess(w, response, http.StatusOK)
}

// UpsertMinMobileVersion godoc
// @Summary Upsert minimum mobile version
// @Description Upsert minimum mobile version
// @Tags MinMobileVersion
// @Accept json
// @Produce json
// @Security Bearer
// @Param payload body MinMobileVersionPayload true "Minimum mobile version"
// @Success 201 {object} interface{} "Minimum mobile version updated"
// @Failure 400 {object} common.ErrorResponse "Bad request"
// @Router /v1/min-mobile-version [post]
func (h *MinMobileVersionHandler) UpsertMinMobileVersion(w http.ResponseWriter, r *http.Request) {
	var payload MinMobileVersionPayload
	err := json.NewDecoder(r.Body).Decode(&payload)
	if err != nil {
		h.logger.WithError(err).Error("Failed to decode request body")
		common.RespondError(w, err, http.StatusBadRequest)
		return
	}

	var semverRegex = regexp.MustCompile(`^\d+\.\d+\.\d+$`)
	if !semverRegex.MatchString(payload.Ios) || !semverRegex.MatchString(payload.Android) {
		h.logger.Error("Invalid version format")
		common.RespondError(w, fmt.Errorf("invalid version format. Should be in semver format (e.g. 1.0.0)"), http.StatusBadRequest)
		return
	}

	err = h.queries.UpsertMinMobileVersion(r.Context(), postgres.UpsertMinMobileVersionParams{
		IosVersion:     payload.Ios,
		AndroidVersion: payload.Android,
	})
	if err != nil {
		h.logger.WithError(err).Error("Failed to upsert min mobile version")
		common.RespondError(w, err, http.StatusInternalServerError)
		return
	}

	common.RespondSuccess[interface{}](w, nil, http.StatusCreated)
}

// HandleMinMobileVersionActivation godoc
// @Summary Handle minimum mobile version activation
// @Description Handle minimum mobile version activation
// @Tags MinMobileVersion
// @Produce json
// @Security Bearer
// @Param is_active path bool true "Is active"
// @Success 200 {object} interface{} "Minimum mobile version activation handled"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /v1/min-mobile-version/activation/{is_active} [post]
func (h *MinMobileVersionHandler) HandleMinMobileVersionActivation(w http.ResponseWriter, r *http.Request) {
	fmt.Printf("Handling min mobile version activation: %s\n", chi.URLParam(r, "is_active"))

	err := h.queries.HandleMinMobileVersionActivation(r.Context(), chi.URLParam(r, "is_active") == "true")
	if err != nil {
		h.logger.WithError(err).Error("Failed to handle min mobile version activation")
		common.RespondError(w, err, http.StatusInternalServerError)
		return
	}

	common.RespondSuccess[interface{}](w, nil, http.StatusOK)
}

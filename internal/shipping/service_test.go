package shipping

import (
	"context"
	"database/sql"
	"fmt"
	"testing"

	"github.com/izy-mercado/backend/internal/logger"
	"github.com/izy-mercado/backend/pkg/storage/postgres"
	"github.com/jackc/pgtype"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockQueries is a mock implementation of postgres.Queries for testing
type MockQueries struct {
	mock.Mock
}

func (m *MockQueries) CalculateShippingFeeWithDistance(ctx context.Context, params postgres.CalculateShippingFeeWithDistanceParams) (postgres.CalculateShippingFeeWithDistanceRow, error) {
	args := m.Called(ctx, params)
	return args.Get(0).(postgres.CalculateShippingFeeWithDistanceRow), args.Error(1)
}

func (m *MockQueries) GetCompanyShippingRates(ctx context.Context, companyID int32) ([]postgres.CompanyShippingRate, error) {
	args := m.Called(ctx, companyID)
	return args.Get(0).([]postgres.CompanyShippingRate), args.Error(1)
}

func (m *MockQueries) CalculateShippingFeeWithCoordinates(ctx context.Context, params postgres.CalculateShippingFeeWithCoordinatesParams) (postgres.CalculateShippingFeeWithCoordinatesRow, error) {
	args := m.Called(ctx, params)
	return args.Get(0).(postgres.CalculateShippingFeeWithCoordinatesRow), args.Error(1)
}

// MockLogger is a mock implementation of logger.Logger for testing
type MockLogger struct {
	mock.Mock
}

func (m *MockLogger) WithContext(ctx context.Context) logger.LogEntry { return &MockLogEntry{} }
func (m *MockLogger) WithOrderID(orderID string) logger.LogEntry      { return &MockLogEntry{} }
func (m *MockLogger) WithUserExternalID(userExternalID string) logger.LogEntry {
	return &MockLogEntry{}
}
func (m *MockLogger) WithCompanyExternalID(companyExternalID string) logger.LogEntry {
	return &MockLogEntry{}
}
func (m *MockLogger) WithFields(fields map[string]interface{}) logger.LogEntry {
	return &MockLogEntry{}
}
func (m *MockLogger) WithError(err error) logger.LogEntry               { return &MockLogEntry{} }
func (m *MockLogger) WithTrace(traceID string) logger.LogEntry          { return &MockLogEntry{} }
func (m *MockLogger) WithSpanID(spanID string) logger.LogEntry          { return &MockLogEntry{} }
func (m *MockLogger) WithOperation(id, producer string) logger.LogEntry { return &MockLogEntry{} }
func (m *MockLogger) WithSourceLocation(file string, line int, function string) logger.LogEntry {
	return &MockLogEntry{}
}

func (m *MockLogger) Debug(msg string)    {}
func (m *MockLogger) Info(msg string)     {}
func (m *MockLogger) Warn(msg string)     {}
func (m *MockLogger) Error(msg string)    {}
func (m *MockLogger) Critical(msg string) {}

// MockLogEntry is a mock implementation of logger.LogEntry for testing
type MockLogEntry struct{}

func (e *MockLogEntry) WithField(key string, value interface{}) logger.LogEntry  { return e }
func (e *MockLogEntry) WithFields(fields map[string]interface{}) logger.LogEntry { return e }
func (e *MockLogEntry) WithError(err error) logger.LogEntry                      { return e }
func (e *MockLogEntry) WithTrace(traceID string) logger.LogEntry                 { return e }
func (e *MockLogEntry) WithSpanID(spanID string) logger.LogEntry                 { return e }
func (e *MockLogEntry) WithOperation(id, producer string) logger.LogEntry        { return e }
func (e *MockLogEntry) WithSourceLocation(file string, line int, function string) logger.LogEntry {
	return e
}
func (e *MockLogEntry) Debug(msg string)    {}
func (e *MockLogEntry) Info(msg string)     {}
func (e *MockLogEntry) Warn(msg string)     {}
func (e *MockLogEntry) Error(msg string)    {}
func (e *MockLogEntry) Critical(msg string) {}

func TestCalculateShippingFee_Success(t *testing.T) {
	// Setup
	mockQueries := &MockQueries{}
	mockLogger := &MockLogger{}
	service := New(mockQueries, mockLogger)

	ctx := context.Background()
	companyExternalID := "company-123"
	userAddressExternalID := "address-456"

	// Create expected distance value
	expectedDistance := pgtype.Numeric{}
	expectedDistance.Set(5.25) // 5.25 km

	expectedResult := postgres.CalculateShippingFeeWithDistanceRow{
		DistanceKm:          expectedDistance,
		ShippingFeeCentavos: 750, // R$7.50
	}

	// Mock expectations
	mockQueries.On("CalculateShippingFeeWithDistance", ctx, postgres.CalculateShippingFeeWithDistanceParams{
		CompanyExternalID:     companyExternalID,
		UserAddressExternalID: userAddressExternalID,
	}).Return(expectedResult, nil)

	// Execute
	result, err := service.CalculateShippingFee(ctx, companyExternalID, userAddressExternalID)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, 5.25, result.DistanceKm)
	assert.Equal(t, int32(750), result.ShippingFeeCentavos)

	mockQueries.AssertExpectations(t)
}

func TestCalculateShippingFeeForDelivery_PickupMode(t *testing.T) {
	// Setup
	mockQueries := &MockQueries{}
	mockLogger := &MockLogger{}
	service := New(mockQueries, mockLogger)

	ctx := context.Background()
	companyExternalID := "company-123"
	userAddressExternalID := "address-456"
	deliveryMode := "pickup"

	// Execute
	result, err := service.CalculateShippingFeeForDelivery(ctx, companyExternalID, userAddressExternalID, deliveryMode)

	// Assert
	assert.NoError(t, err)
	assert.Equal(t, int32(0), result)

	// No queries should be called for pickup mode
	mockQueries.AssertNotCalled(t, "CalculateShippingFeeWithDistance")
}

func TestCalculateShippingFeeForDelivery_DeliveryMode(t *testing.T) {
	// Setup
	mockQueries := &MockQueries{}
	mockLogger := &MockLogger{}
	service := New(mockQueries, mockLogger)

	ctx := context.Background()
	companyExternalID := "company-123"
	userAddressExternalID := "address-456"
	deliveryMode := "delivery"

	// Create expected distance value
	expectedDistance := pgtype.Numeric{}
	expectedDistance.Set(3.75) // 3.75 km

	expectedResult := postgres.CalculateShippingFeeWithDistanceRow{
		DistanceKm:          expectedDistance,
		ShippingFeeCentavos: 500, // R$5.00
	}

	// Mock expectations
	mockQueries.On("CalculateShippingFeeWithDistance", ctx, postgres.CalculateShippingFeeWithDistanceParams{
		CompanyExternalID:     companyExternalID,
		UserAddressExternalID: userAddressExternalID,
	}).Return(expectedResult, nil)

	// Execute
	result, err := service.CalculateShippingFeeForDelivery(ctx, companyExternalID, userAddressExternalID, deliveryMode)

	// Assert
	assert.NoError(t, err)
	assert.Equal(t, int32(500), result)

	mockQueries.AssertExpectations(t)
}

func TestValidateShippingRateConfiguration_Success(t *testing.T) {
	// Setup
	mockQueries := &MockQueries{}
	mockLogger := &MockLogger{}
	service := New(mockQueries, mockLogger)

	ctx := context.Background()
	companyID := int32(123)

	// Create mock shipping rates
	rate1DistanceMin := pgtype.Numeric{}
	rate1DistanceMin.Set(0.0)
	rate1DistanceMax := pgtype.Numeric{}
	rate1DistanceMax.Set(5.0)

	rate2DistanceMin := pgtype.Numeric{}
	rate2DistanceMin.Set(5.0)
	rate2DistanceMax := pgtype.Numeric{}
	rate2DistanceMax.Set(10.0)

	expectedRates := []postgres.CompanyShippingRate{
		{
			ID:            1,
			CompanyID:     companyID,
			DistanceMinKm: rate1DistanceMin,
			DistanceMaxKm: rate1DistanceMax,
			FeeCentavos:   500,
		},
		{
			ID:            2,
			CompanyID:     companyID,
			DistanceMinKm: rate2DistanceMin,
			DistanceMaxKm: rate2DistanceMax,
			FeeCentavos:   750,
		},
	}

	// Mock expectations
	mockQueries.On("GetCompanyShippingRates", ctx, companyID).Return(expectedRates, nil)

	// Execute
	err := service.ValidateShippingRateConfiguration(ctx, companyID)

	// Assert
	assert.NoError(t, err)
	mockQueries.AssertExpectations(t)
}

func TestValidateShippingRateConfiguration_NoRates(t *testing.T) {
	// Setup
	mockQueries := &MockQueries{}
	mockLogger := &MockLogger{}
	service := New(mockQueries, mockLogger)

	ctx := context.Background()
	companyID := int32(123)

	// Mock expectations - return empty rates
	mockQueries.On("GetCompanyShippingRates", ctx, companyID).Return([]postgres.CompanyShippingRate{}, nil)

	// Execute
	err := service.ValidateShippingRateConfiguration(ctx, companyID)

	// Assert
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "company has no shipping rates configured")
	mockQueries.AssertExpectations(t)
}

func TestGetCompanyShippingRates_Success(t *testing.T) {
	// Setup
	mockQueries := &MockQueries{}
	mockLogger := &MockLogger{}
	service := New(mockQueries, mockLogger)

	ctx := context.Background()
	companyID := int32(123)

	// Create mock shipping rates
	rate1DistanceMin := pgtype.Numeric{}
	rate1DistanceMin.Set(0.0)
	rate1DistanceMax := pgtype.Numeric{}
	rate1DistanceMax.Set(5.0)

	expectedRates := []postgres.CompanyShippingRate{
		{
			ID:            1,
			CompanyID:     companyID,
			DistanceMinKm: rate1DistanceMin,
			DistanceMaxKm: rate1DistanceMax,
			FeeCentavos:   500,
		},
	}

	// Mock expectations
	mockQueries.On("GetCompanyShippingRates", ctx, companyID).Return(expectedRates, nil)

	// Execute
	result, err := service.GetCompanyShippingRates(ctx, companyID)

	// Assert
	assert.NoError(t, err)
	assert.Equal(t, expectedRates, result)
	mockQueries.AssertExpectations(t)
}

// Additional comprehensive test cases for various distance scenarios

func TestCalculateShippingFee_VariousDistanceRanges(t *testing.T) {
	testCases := []struct {
		name                string
		distance            float64
		expectedShippingFee int32
		description         string
	}{
		{
			name:                "Very close distance - 0.5km",
			distance:            0.5,
			expectedShippingFee: 300, // R$3.00
			description:         "Short distance within city center",
		},
		{
			name:                "Medium distance - 5.2km",
			distance:            5.2,
			expectedShippingFee: 500, // R$5.00
			description:         "Medium distance to suburbs",
		},
		{
			name:                "Long distance - 12.8km",
			distance:            12.8,
			expectedShippingFee: 800, // R$8.00
			description:         "Long distance to outskirts",
		},
		{
			name:                "Very long distance - 25.0km",
			distance:            25.0,
			expectedShippingFee: 1200, // R$12.00
			description:         "Very long distance to neighboring city",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Setup
			mockQueries := &MockQueries{}
			mockLogger := &MockLogger{}
			service := New(mockQueries, mockLogger)

			ctx := context.Background()
			companyExternalID := "company-123"
			userAddressExternalID := "address-456"

			// Create expected distance value
			expectedDistance := pgtype.Numeric{}
			expectedDistance.Set(tc.distance)

			expectedResult := postgres.CalculateShippingFeeWithDistanceRow{
				DistanceKm:          expectedDistance,
				ShippingFeeCentavos: tc.expectedShippingFee,
			}

			// Mock expectations
			mockQueries.On("CalculateShippingFeeWithDistance", ctx, postgres.CalculateShippingFeeWithDistanceParams{
				CompanyExternalID:     companyExternalID,
				UserAddressExternalID: userAddressExternalID,
			}).Return(expectedResult, nil)

			// Execute
			result, err := service.CalculateShippingFee(ctx, companyExternalID, userAddressExternalID)

			// Assert
			assert.NoError(t, err)
			assert.NotNil(t, result)
			assert.Equal(t, tc.distance, result.DistanceKm)
			assert.Equal(t, tc.expectedShippingFee, result.ShippingFeeCentavos)

			mockQueries.AssertExpectations(t)
		})
	}
}

func TestCalculateShippingFee_EdgeCases(t *testing.T) {
	testCases := []struct {
		name                string
		distance            float64
		expectedShippingFee int32
		description         string
	}{
		{
			name:                "Zero distance - same location",
			distance:            0.0,
			expectedShippingFee: 200, // R$2.00 minimum fee
			description:         "Company and delivery address are at the same location",
		},
		{
			name:                "Exact boundary distance - 10.0km",
			distance:            10.0,
			expectedShippingFee: 700, // R$7.00
			description:         "Distance exactly at rate boundary",
		},
		{
			name:                "Just over boundary - 10.01km",
			distance:            10.01,
			expectedShippingFee: 800, // R$8.00 - next tier
			description:         "Distance just over rate boundary triggers next tier",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Setup
			mockQueries := &MockQueries{}
			mockLogger := &MockLogger{}
			service := New(mockQueries, mockLogger)

			ctx := context.Background()
			companyExternalID := "company-123"
			userAddressExternalID := "address-456"

			// Create expected distance value
			expectedDistance := pgtype.Numeric{}
			expectedDistance.Set(tc.distance)

			expectedResult := postgres.CalculateShippingFeeWithDistanceRow{
				DistanceKm:          expectedDistance,
				ShippingFeeCentavos: tc.expectedShippingFee,
			}

			// Mock expectations
			mockQueries.On("CalculateShippingFeeWithDistance", ctx, postgres.CalculateShippingFeeWithDistanceParams{
				CompanyExternalID:     companyExternalID,
				UserAddressExternalID: userAddressExternalID,
			}).Return(expectedResult, nil)

			// Execute
			result, err := service.CalculateShippingFee(ctx, companyExternalID, userAddressExternalID)

			// Assert
			assert.NoError(t, err)
			assert.NotNil(t, result)
			assert.Equal(t, tc.distance, result.DistanceKm)
			assert.Equal(t, tc.expectedShippingFee, result.ShippingFeeCentavos)

			mockQueries.AssertExpectations(t)
		})
	}
}

// Error handling test cases

func TestCalculateShippingFee_DatabaseError(t *testing.T) {
	// Setup
	mockQueries := &MockQueries{}
	mockLogger := &MockLogger{}
	service := New(mockQueries, mockLogger)

	ctx := context.Background()
	companyExternalID := "company-123"
	userAddressExternalID := "address-456"

	// Mock expectations - return database error
	mockQueries.On("CalculateShippingFeeWithDistance", ctx, postgres.CalculateShippingFeeWithDistanceParams{
		CompanyExternalID:     companyExternalID,
		UserAddressExternalID: userAddressExternalID,
	}).Return(postgres.CalculateShippingFeeWithDistanceRow{}, fmt.Errorf("database connection error"))

	// Execute
	result, err := service.CalculateShippingFee(ctx, companyExternalID, userAddressExternalID)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "failed to calculate shipping fee")
	mockQueries.AssertExpectations(t)
}

func TestCalculateShippingFee_InvalidCompanyID(t *testing.T) {
	// Setup
	mockQueries := &MockQueries{}
	mockLogger := &MockLogger{}
	service := New(mockQueries, mockLogger)

	ctx := context.Background()
	companyExternalID := "invalid-company"
	userAddressExternalID := "address-456"

	// Mock expectations - return no rows error (company not found)
	mockQueries.On("CalculateShippingFeeWithDistance", ctx, postgres.CalculateShippingFeeWithDistanceParams{
		CompanyExternalID:     companyExternalID,
		UserAddressExternalID: userAddressExternalID,
	}).Return(postgres.CalculateShippingFeeWithDistanceRow{}, sql.ErrNoRows)

	// Execute
	result, err := service.CalculateShippingFee(ctx, companyExternalID, userAddressExternalID)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "failed to calculate shipping fee")
	mockQueries.AssertExpectations(t)
}

func TestCalculateShippingFee_InvalidAddressID(t *testing.T) {
	// Setup
	mockQueries := &MockQueries{}
	mockLogger := &MockLogger{}
	service := New(mockQueries, mockLogger)

	ctx := context.Background()
	companyExternalID := "company-123"
	userAddressExternalID := "invalid-address"

	// Mock expectations - return no rows error (address not found)
	mockQueries.On("CalculateShippingFeeWithDistance", ctx, postgres.CalculateShippingFeeWithDistanceParams{
		CompanyExternalID:     companyExternalID,
		UserAddressExternalID: userAddressExternalID,
	}).Return(postgres.CalculateShippingFeeWithDistanceRow{}, sql.ErrNoRows)

	// Execute
	result, err := service.CalculateShippingFee(ctx, companyExternalID, userAddressExternalID)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "failed to calculate shipping fee")
	mockQueries.AssertExpectations(t)
}

// Performance and load testing scenarios

func TestCalculateShippingFeeForDelivery_ConcurrentRequests(t *testing.T) {
	// Setup
	mockQueries := &MockQueries{}
	mockLogger := &MockLogger{}
	service := New(mockQueries, mockLogger)

	ctx := context.Background()
	companyExternalID := "company-123"
	userAddressExternalID := "address-456"
	deliveryMode := "delivery"

	// Create expected distance value
	expectedDistance := pgtype.Numeric{}
	expectedDistance.Set(5.0)

	expectedResult := postgres.CalculateShippingFeeWithDistanceRow{
		DistanceKm:          expectedDistance,
		ShippingFeeCentavos: 500,
	}

	// Mock expectations for multiple concurrent calls
	mockQueries.On("CalculateShippingFeeWithDistance", ctx, postgres.CalculateShippingFeeWithDistanceParams{
		CompanyExternalID:     companyExternalID,
		UserAddressExternalID: userAddressExternalID,
	}).Return(expectedResult, nil).Times(10)

	// Execute concurrent requests
	const numGoroutines = 10
	results := make(chan int32, numGoroutines)
	errors := make(chan error, numGoroutines)

	for i := 0; i < numGoroutines; i++ {
		go func() {
			result, err := service.CalculateShippingFeeForDelivery(ctx, companyExternalID, userAddressExternalID, deliveryMode)
			if err != nil {
				errors <- err
			} else {
				results <- result
			}
		}()
	}

	// Collect results
	for i := 0; i < numGoroutines; i++ {
		select {
		case result := <-results:
			assert.Equal(t, int32(500), result)
		case err := <-errors:
			t.Errorf("Unexpected error in concurrent test: %v", err)
		}
	}

	mockQueries.AssertExpectations(t)
}

// Business logic validation tests

func TestCalculateShippingFeeForDelivery_MultipleDeliveryModes(t *testing.T) {
	testCases := []struct {
		name         string
		deliveryMode string
		expectedFee  int32
		shouldCallDB bool
	}{
		{
			name:         "Pickup mode - no shipping fee",
			deliveryMode: "pickup",
			expectedFee:  0,
			shouldCallDB: false,
		},
		{
			name:         "Delivery mode - calculate shipping fee",
			deliveryMode: "delivery",
			expectedFee:  500,
			shouldCallDB: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Setup
			mockQueries := &MockQueries{}
			mockLogger := &MockLogger{}
			service := New(mockQueries, mockLogger)

			ctx := context.Background()
			companyExternalID := "company-123"
			userAddressExternalID := "address-456"

			if tc.shouldCallDB {
				// Create expected distance value
				expectedDistance := pgtype.Numeric{}
				expectedDistance.Set(5.0)

				expectedResult := postgres.CalculateShippingFeeWithDistanceRow{
					DistanceKm:          expectedDistance,
					ShippingFeeCentavos: tc.expectedFee,
				}

				// Mock expectations
				mockQueries.On("CalculateShippingFeeWithDistance", ctx, postgres.CalculateShippingFeeWithDistanceParams{
					CompanyExternalID:     companyExternalID,
					UserAddressExternalID: userAddressExternalID,
				}).Return(expectedResult, nil)
			}

			// Execute
			result, err := service.CalculateShippingFeeForDelivery(ctx, companyExternalID, userAddressExternalID, tc.deliveryMode)

			// Assert
			assert.NoError(t, err)
			assert.Equal(t, tc.expectedFee, result)

			if tc.shouldCallDB {
				mockQueries.AssertExpectations(t)
			} else {
				mockQueries.AssertNotCalled(t, "CalculateShippingFeeWithDistance")
			}
		})
	}
}

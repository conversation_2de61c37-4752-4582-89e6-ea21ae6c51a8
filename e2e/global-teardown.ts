import { FullConfig } from '@playwright/test';
import * as fs from 'fs';
import * as path from 'path';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting global E2E test teardown...');

  try {
    // Read service configuration to get backend service reference
    const serviceConfigPath = path.join(process.cwd(), 'test-results', 'service-config.json');

    if (fs.existsSync(serviceConfigPath)) {
      const serviceConfig = JSON.parse(fs.readFileSync(serviceConfigPath, 'utf8'));
      console.log(`🔍 Found service configuration from ${serviceConfig.timestamp}`);
    }

    // Import and stop backend service
    // Note: The BackendService instance is managed globally, so we need to access it
    const { BackendService } = await import('./services/backend-service');

    // Since we can't directly access the global instance, we'll rely on Docker cleanup
    // Testcontainers automatically cleans up containers when the process exits
    console.log('🐳 Testcontainers will automatically clean up containers...');

    // Clean up test artifacts
    const testResultsDir = path.join(process.cwd(), 'test-results');
    if (fs.existsSync(serviceConfigPath)) {
      fs.unlinkSync(serviceConfigPath);
      console.log('🗑️ Cleaned up service configuration file');
    }

    console.log('✅ Global teardown completed successfully');

  } catch (error) {
    console.error('❌ Global teardown failed:', error);
    // Don't throw error to avoid failing the test run
  }
}

export default globalTeardown;

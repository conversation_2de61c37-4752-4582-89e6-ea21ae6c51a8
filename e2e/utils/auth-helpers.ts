import { Page, expect } from '@playwright/test';
import { TestHelpers } from './test-helpers';

/**
 * Authentication helper utilities for E2E tests
 */

export interface LoginCredentials {
  email: string;
  loginCode: string;
}

export interface TestUser {
  email: string;
  role: 'admin' | 'partner';
  loginCode: string;
}

export class AuthHelpers {
  private helpers: TestHelpers;

  constructor(private page: Page) {
    this.helpers = new TestHelpers(page);
  }

  /**
   * Navigate to login page
   */
  async goToLogin() {
    await this.page.goto('/login');
    await this.helpers.waitForPageLoad();
    await expect(this.page).toHaveURL(/.*\/login/);
  }

  /**
   * Send login code to email
   */
  async sendLoginCode(email: string) {
    await this.goToLogin();
    
    // Fill email field
    await this.helpers.fillField('[data-testid="email-input"], input[type="email"]', email);
    
    // Click send code button
    const sendButton = this.page.locator('[data-testid="send-code-button"], button:has-text("Enviar Código")').first();
    await sendButton.click();
    
    // Wait for success message or next step
    await this.helpers.waitForToast();
    
    // Wait for login code input to appear
    await this.helpers.waitForElement('[data-testid="login-code-input"], input[placeholder*="código"]');
  }

  /**
   * Complete login with email and code
   */
  async login(credentials: LoginCredentials) {
    // Send login code first
    await this.sendLoginCode(credentials.email);
    
    // Fill login code
    await this.helpers.fillField('[data-testid="login-code-input"], input[placeholder*="código"]', credentials.loginCode);
    
    // Click login button
    const loginButton = this.page.locator('[data-testid="login-button"], button:has-text("Entrar")').first();
    
    // Click login button and wait for navigation
    await loginButton.click();

    // Wait for navigation with a more flexible approach
    try {
      await this.page.waitForURL(/\/(admin|partner)\/dashboard/, { timeout: 10000 });
    } catch (error) {
      // If URL-based navigation fails, wait for page load
      await this.page.waitForLoadState('networkidle', { timeout: 5000 });
    }
    
    await this.helpers.waitForLoadingToComplete();
  }

  /**
   * Login as admin user
   */
  async loginAsAdmin() {
    const adminCredentials: LoginCredentials = {
      email: process.env.TEST_ADMIN_EMAIL || '<EMAIL>',
      loginCode: process.env.TEST_ADMIN_CODE || '123456'
    };
    
    await this.login(adminCredentials);
    
    // Verify we're on admin dashboard
    await expect(this.page).toHaveURL(/.*\/admin\/dashboard/);
    await this.helpers.waitForPageLoad();
  }

  /**
   * Login as partner user
   */
  async loginAsPartner() {
    const partnerCredentials: LoginCredentials = {
      email: process.env.TEST_PARTNER_EMAIL || '<EMAIL>',
      loginCode: process.env.TEST_PARTNER_CODE || '123456'
    };
    
    await this.login(partnerCredentials);
    
    // Verify we're on partner dashboard
    await expect(this.page).toHaveURL(/.*\/partner\/dashboard/);
    await this.helpers.waitForPageLoad();
  }

  /**
   * Logout user
   */
  async logout() {
    // Look for logout button in various possible locations
    const logoutSelectors = [
      '[data-testid="logout-button"]',
      'button:has-text("Sair")',
      'button:has-text("Logout")',
      '[aria-label="Logout"]',
      '[aria-label="Sair"]'
    ];
    
    let logoutButton = null;
    for (const selector of logoutSelectors) {
      logoutButton = this.page.locator(selector).first();
      if (await logoutButton.isVisible()) {
        break;
      }
    }
    
    if (logoutButton && await logoutButton.isVisible()) {
      await Promise.all([
        this.page.waitForNavigation({ waitUntil: 'networkidle' }),
        logoutButton.click()
      ]);
    } else {
      // Fallback: clear storage and navigate to login
      await this.helpers.clearStorage();
      await this.page.goto('/login');
    }
    
    // Verify we're back on login page
    await expect(this.page).toHaveURL(/.*\/login/);
  }

  /**
   * Check if user is authenticated
   */
  async isAuthenticated(): Promise<boolean> {
    const token = await this.helpers.getLocalStorage('token');
    return !!token;
  }

  /**
   * Get current user role from localStorage or URL
   */
  async getCurrentUserRole(): Promise<'admin' | 'partner' | null> {
    const url = this.page.url();
    
    if (url.includes('/admin/')) {
      return 'admin';
    } else if (url.includes('/partner/')) {
      return 'partner';
    }
    
    return null;
  }

  /**
   * Mock authentication state for testing
   */
  async mockAuthState(user: TestUser) {
    // Set up API mocks FIRST, before setting localStorage
    await this.mockAuthenticationAPIs(user);

    // Ensure we're on a proper HTTP URL before setting localStorage
    const currentUrl = this.page.url();
    const baseUrl = process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:4173';

    // If we're not on a proper HTTP URL, navigate to the base URL first
    if (!currentUrl.startsWith('http://') && !currentUrl.startsWith('https://')) {
      await this.page.goto(baseUrl);
      await this.helpers.waitForPageLoad();
    }

    // Mock tokens with proper names and timestamps (as required by tokenManager.ts)
    const mockTokens = {
      access_token: 'mock-access-token-' + Date.now(),
      refresh_token: 'mock-refresh-token-' + Date.now()
    };

    const currentTimestamp = Date.now().toString();

    // Set tokens with the exact keys expected by the frontend
    console.log('🔐 Setting localStorage tokens for user:', user.email);
    await this.helpers.setLocalStorage('token', mockTokens.access_token);
    await this.helpers.setLocalStorage('refreshToken', mockTokens.refresh_token); // Note: refreshToken not refresh_token
    await this.helpers.setLocalStorage('tokenTimestamp', currentTimestamp);
    await this.helpers.setLocalStorage('refreshTokenTimestamp', currentTimestamp);

    // Mock user data
    const mockUserData = {
      email: user.email,
      role: user.role
    };

    await this.helpers.setLocalStorage('user', JSON.stringify(mockUserData));

    // Verify tokens were set
    const storedToken = await this.helpers.getLocalStorage('token');
    const storedRefreshToken = await this.helpers.getLocalStorage('refreshToken');
    console.log('✅ Stored tokens:', {
      token: storedToken ? 'SET' : 'NOT SET',
      refreshToken: storedRefreshToken ? 'SET' : 'NOT SET'
    });
  }

  /**
   * Mock authentication API responses
   */
  async mockAuthenticationAPIs(user: TestUser) {
    // Mock /v1/user/me endpoint with broader pattern matching
    await this.page.route('**/v1/user/me*', (route) => {
      console.log('🔍 Intercepted /v1/user/me request:', route.request().url());
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          data: {
            id: 1,
            email: user.email,
            role: user.role,
            name: user.email.split('@')[0],
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          }
        })
      });
    });

    // Mock /v1/company/my-companies endpoint with broader pattern matching
    await this.page.route('**/v1/company/my-companies*', (route) => {
      console.log('🔍 Intercepted /v1/company/my-companies request:', route.request().url());
      console.log('🔍 Current user role:', user.role);

      if (user.role === 'admin') {
        const adminResponse = {
          data: {
            company_external_ids: [],
            owner_external_id: 'admin-1',
            dashboard_url: '/admin/dashboard'
          }
        };
        console.log('🔍 Returning admin response:', JSON.stringify(adminResponse, null, 2));

        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify(adminResponse)
        });
      } else {
        const partnerResponse = {
          data: {
            company_external_ids: ['company-1'],
            owner_external_id: 'partner-1',
            dashboard_url: '/partner/dashboard'
          }
        };
        console.log('🔍 Returning partner response:', JSON.stringify(partnerResponse, null, 2));

        route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify(partnerResponse)
        });
      }
    });

    // Mock common admin endpoints with test data
    if (user.role === 'admin') {
      await this.mockAdminAPIs();
    }
  }

  /**
   * Mock admin-specific API endpoints with test data
   */
  async mockAdminAPIs() {
    // Mock companies list with broader pattern matching
    await this.page.route('**/v1/company/all*', (route) => {
      console.log('🔍 Intercepted /v1/company/all request:', route.request().url());
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          data: [
            {
              id: 1,
              external_id: 'company-1',
              name: 'Test Company 1',
              email: '<EMAIL>',
              document: '12345678901',
              phone: '(11) 99999-9999',
              status: 'active',
              created_at: new Date().toISOString()
            },
            {
              id: 2,
              external_id: 'company-2',
              name: 'Test Company 2',
              email: '<EMAIL>',
              document: '12345678902',
              phone: '(11) 99999-9998',
              status: 'active',
              created_at: new Date().toISOString()
            }
          ]
        })
      });
    });

    // Mock products list with broader pattern matching
    await this.page.route('**/v1/product*', (route) => {
      console.log('🔍 Intercepted /v1/product request:', route.request().url());
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          data: [
            {
              id: 1,
              external_id: 'product-1',
              name: 'Test Product 1',
              brand: 'Test Brand',
              price: 1999, // R$ 19,99 in cents
              ean: '1234567890123',
              status: 'active',
              created_at: new Date().toISOString()
            },
            {
              id: 2,
              external_id: 'product-2',
              name: 'Test Product 2',
              brand: 'Test Brand',
              price: 2999, // R$ 29,99 in cents
              ean: '1234567890124',
              status: 'active',
              created_at: new Date().toISOString()
            }
          ],
          totalItems: 2,
          totalPages: 1,
          currentPage: 1
        })
      });
    });

    // Mock users list with broader pattern matching (but exclude /v1/user/me)
    await this.page.route('**/v1/user*', (route) => {
      const url = route.request().url();
      // Don't intercept /v1/user/me, only /v1/user (list endpoint)
      if (url.includes('/me')) {
        route.continue();
        return;
      }
      console.log('🔍 Intercepted /v1/user list request:', url);
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          data: [
            {
              id: 1,
              email: '<EMAIL>',
              role: 'admin',
              name: 'Admin User',
              status: 'active',
              created_at: new Date().toISOString()
            },
            {
              id: 2,
              email: '<EMAIL>',
              role: 'partner',
              name: 'Partner User',
              status: 'active',
              created_at: new Date().toISOString()
            }
          ]
        })
      });
    });

    // Mock company creation endpoint
    await this.page.route('**/v1/company', async (route) => {
      if (route.request().method() === 'POST') {
        console.log('🔍 Intercepted POST /v1/company request:', route.request().url());

        try {
          // Handle FormData request (multipart/form-data)
          const requestBody = route.request().postData();
          console.log('📝 Company creation request body (FormData):', requestBody?.substring(0, 200) + '...');

          // Extract name from FormData for response
          let companyName = 'New Test Company';
          if (requestBody) {
            const nameMatch = requestBody.match(/name="name"\s*\n\s*([^\n-]+)/);
            if (nameMatch) {
              companyName = nameMatch[1].trim();
            }
          }

          await route.fulfill({
            status: 201,
            contentType: 'application/json',
            body: JSON.stringify({
              data: 'new-company-123' // This is what the frontend expects for navigation
            })
          });
          console.log('✅ Company creation mock response sent successfully');
        } catch (error) {
          console.error('❌ Error in company creation mock:', error);
          await route.fulfill({
            status: 500,
            contentType: 'application/json',
            body: JSON.stringify({ error: 'Mock error' })
          });
        }
      } else {
        await route.continue();
      }
    });

    // Mock product creation endpoint
    await this.page.route('**/v1/product', async (route) => {
      if (route.request().method() === 'POST') {
        console.log('🔍 Intercepted POST /v1/product request:', route.request().url());

        try {
          // Handle FormData request (multipart/form-data)
          const requestBody = route.request().postData();
          console.log('📝 Product creation request body (FormData):', requestBody?.substring(0, 200) + '...');

          // Extract name from FormData for response
          let productName = 'New Test Product';
          if (requestBody) {
            const nameMatch = requestBody.match(/name="name"\s*\n\s*([^\n-]+)/);
            if (nameMatch) {
              productName = nameMatch[1].trim();
            }
          }

          await route.fulfill({
            status: 201,
            contentType: 'application/json',
            body: JSON.stringify({
              data: {
                id: 3,
                external_id: 'new-product-123',
                name: productName,
                brand: 'Test Brand',
                price: 2999,
                status: 'active',
                created_at: new Date().toISOString()
              }
            })
          });
          console.log('✅ Product creation mock response sent successfully');
        } catch (error) {
          console.error('❌ Error in product creation mock:', error);
          await route.fulfill({
            status: 500,
            contentType: 'application/json',
            body: JSON.stringify({ error: 'Mock error' })
          });
        }
      } else {
        await route.continue();
      }
    });

    // Mock user creation endpoint
    await this.page.route('**/v1/user', async (route) => {
      const url = route.request().url();
      if (route.request().method() === 'POST' && !url.includes('/me') && !url.includes('/check-email')) {
        console.log('🔍 Intercepted POST /v1/user request:', url);

        try {
          // Handle JSON request (users form sends JSON, not FormData)
          const requestBody = route.request().postDataJSON();
          console.log('📝 User creation request body (JSON):', requestBody);

          await route.fulfill({
            status: 201,
            contentType: 'application/json',
            body: JSON.stringify({
              data: {
                id: 3,
                external_id: 'new-user-123',
                name: requestBody?.name || 'New Test User',
                email: requestBody?.email || '<EMAIL>',
                cpf: requestBody?.cpf || '12345678901',
                phone: requestBody?.phone || '+5511999999999',
                status: 'active',
                created_at: new Date().toISOString()
              }
            })
          });
          console.log('✅ User creation mock response sent successfully');
        } catch (error) {
          console.error('❌ Error in user creation mock:', error);
          await route.fulfill({
            status: 500,
            contentType: 'application/json',
            body: JSON.stringify({ error: 'Mock error' })
          });
        }
      } else {
        await route.continue();
      }
    });

    // Mock email check endpoint
    await this.page.route('**/v1/user/check-email*', (route) => {
      console.log('🔍 Intercepted /v1/user/check-email request:', route.request().url());
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          data: false // Email doesn't exist
        })
      });
    });

    // Mock user search endpoint
    await this.page.route('**/v1/user/search/**', async (route) => {
      console.log('🔍 Intercepted /v1/user/search request:', route.request().url());
      const url = new URL(route.request().url());
      const query = url.searchParams.get('q') || '';

      // Return matching users based on search query
      const users = [
        {
          id: 1,
          external_id: 'user-1',
          name: 'Admin User',
          email: '<EMAIL>',
          status: 'active',
          created_at: new Date().toISOString()
        },
        {
          id: 2,
          external_id: 'user-2',
          name: 'Partner User',
          email: '<EMAIL>',
          status: 'active',
          created_at: new Date().toISOString()
        }
      ];

      // Filter users based on search query
      const filteredUsers = query ?
        users.filter(user =>
          user.name.toLowerCase().includes(query.toLowerCase()) ||
          user.email.toLowerCase().includes(query.toLowerCase())
        ) : users;

      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          data: filteredUsers,
          meta: {
            total: filteredUsers.length,
            page: 1,
            limit: 10
          }
        })
      });
      console.log('✅ User search mock response sent with', filteredUsers.length, 'users');
    });

    // Mock partner company details endpoint
    await this.page.route('**/v1/company/company-1', async (route) => {
      console.log('🔍 Intercepted /v1/company/company-1 request:', route.request().url());

      const companyResponse = {
        data: {
          id: 1,
          external_id: 'company-1',
          name: 'Test Company',
          email: '<EMAIL>',
          cnpj: '12.345.678/0001-90',
          phone: '+5511999999999',
          address: 'Rua Teste, 123',
          city: 'São Paulo',
          state: 'SP',
          zip_code: '01234-567',
          balance_cents: 15000, // R$ 150,00
          commission_rate: 15,
          status: 'active',
          created_at: new Date().toISOString()
        }
      };

      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(companyResponse)
      });
      console.log('✅ Company details mock response sent');
    });

    // Mock partner orders endpoint
    await this.page.route('**/v1/order/company/company-1**', async (route) => {
      console.log('🔍 Intercepted partner orders request:', route.request().url());

      const ordersResponse = {
        data: [
          {
            id: 1,
            external_id: 'order-1',
            customer_name: 'João Silva',
            customer_email: '<EMAIL>',
            total_cents: 5999, // R$ 59,99
            status: 'delivered',
            delivery_mode: 'delivery',
            created_at: new Date().toISOString()
          },
          {
            id: 2,
            external_id: 'order-2',
            customer_name: 'Maria Santos',
            customer_email: '<EMAIL>',
            total_cents: 3499, // R$ 34,99
            status: 'confirmed',
            delivery_mode: 'pickup',
            created_at: new Date().toISOString()
          }
        ],
        meta: {
          total: 2,
          page: 1,
          limit: 10
        }
      };

      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(ordersResponse)
      });
      console.log('✅ Partner orders mock response sent with', ordersResponse.data.length, 'orders');
    });

    // Mock partner withdrawal endpoint
    await this.page.route('**/v1/company/company-1/withdraw', async (route) => {
      console.log('🔍 Intercepted withdrawal request:', route.request().url());

      const withdrawalResponse = {
        data: {
          id: 1,
          external_id: 'withdrawal-1',
          amount_cents: 15000, // R$ 150,00
          status: 'completed',
          created_at: new Date().toISOString()
        }
      };

      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(withdrawalResponse)
      });
      console.log('✅ Withdrawal mock response sent');
    });

    // Mock withdrawal history endpoint
    await this.page.route('**/v1/company/company-1/withdrawals**', async (route) => {
      console.log('🔍 Intercepted withdrawal history request:', route.request().url());

      const historyResponse = {
        data: [
          {
            id: 1,
            external_id: 'withdrawal-1',
            amount_cents: 10000, // R$ 100,00
            status: 'completed',
            created_at: new Date(Date.now() - 86400000).toISOString() // Yesterday
          },
          {
            id: 2,
            external_id: 'withdrawal-2',
            amount_cents: 5000, // R$ 50,00
            status: 'completed',
            created_at: new Date(Date.now() - *********).toISOString() // 2 days ago
          }
        ],
        meta: {
          total: 2,
          page: 1,
          limit: 10
        }
      };

      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify(historyResponse)
      });
      console.log('✅ Withdrawal history mock response sent');
    });

    // Mock categories endpoint for products
    await this.page.route('**/v1/category*', (route) => {
      console.log('🔍 Intercepted /v1/category request:', route.request().url());
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          data: [
            {
              id: 1,
              external_id: 'cat-1',
              name: 'Electronics'
            },
            {
              id: 2,
              external_id: 'cat-2',
              name: 'Books'
            }
          ]
        })
      });
    });
  }

  /**
   * Verify authentication error handling
   */
  async verifyAuthError(expectedMessage?: string) {
    // Wait for error message to appear
    const errorMessage = this.page.locator('[data-testid="error-message"], .error, [role="alert"]').first();
    await errorMessage.waitFor({ state: 'visible', timeout: 5000 });
    
    if (expectedMessage) {
      await expect(errorMessage).toContainText(expectedMessage);
    }
    
    // Should redirect to login page on auth error
    await expect(this.page).toHaveURL(/.*\/login/);
  }
}

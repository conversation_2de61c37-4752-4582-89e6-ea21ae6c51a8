import { Page, expect } from '@playwright/test';

/**
 * Test helper utilities for E2E tests
 */

export class TestHelpers {
  constructor(private page: Page) {}

  /**
   * Wait for the page to load completely
   */
  async waitForPageLoad() {
    await this.page.waitForLoadState('networkidle');
    await this.page.waitForLoadState('domcontentloaded');
  }

  /**
   * Wait for an element to be visible and enabled
   */
  async waitForElement(selector: string, timeout = 10000) {
    const element = this.page.locator(selector);
    await element.waitFor({ state: 'visible', timeout });
    return element;
  }

  /**
   * Fill form field with validation
   */
  async fillField(selector: string, value: string) {
    const field = await this.waitForElement(selector);
    await field.clear();
    await field.fill(value);
    await expect(field).toHaveValue(value);
  }

  /**
   * Click button and wait for navigation or response
   */
  async clickAndWait(selector: string, waitForNavigation = false) {
    const button = await this.waitForElement(selector);
    
    if (waitForNavigation) {
      await Promise.all([
        this.page.waitForNavigation({ waitUntil: 'networkidle' }),
        button.click()
      ]);
    } else {
      await button.click();
    }
  }

  /**
   * Wait for toast notification to appear
   */
  async waitForToast(message?: string, timeout = 5000) {
    // The app uses both Sonner and Radix UI toasts
    // Sonner: .sonner-toast, [data-sonner-toast]
    // Radix UI: [data-state="open"][role="status"], .toast
    const toast = this.page.locator('[data-sonner-toast], .sonner-toast, [data-state="open"][role="status"], .toast, [role="alert"]').first();
    await toast.waitFor({ state: 'visible', timeout });

    if (message) {
      await expect(toast).toContainText(message);
    }

    return toast;
  }

  /**
   * Wait for loading spinner to disappear
   */
  async waitForLoadingToComplete() {
    const loadingSpinner = this.page.locator('[data-testid="loading"], .loading, .spinner');
    await loadingSpinner.waitFor({ state: 'hidden', timeout: 30000 }).catch(() => {
      // Ignore if no loading spinner is found
    });
  }

  /**
   * Check if user is on login page
   */
  async isOnLoginPage() {
    await this.waitForPageLoad();
    const url = this.page.url();
    return url.includes('/login');
  }

  /**
   * Check if user is on admin dashboard
   */
  async isOnAdminDashboard() {
    await this.waitForPageLoad();
    const url = this.page.url();
    return url.includes('/admin/dashboard');
  }

  /**
   * Check if user is on partner dashboard
   */
  async isOnPartnerDashboard() {
    await this.waitForPageLoad();
    const url = this.page.url();
    return url.includes('/partner/dashboard');
  }

  /**
   * Take screenshot with timestamp
   */
  async takeScreenshot(name: string) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    await this.page.screenshot({ 
      path: `test-results/screenshots/${name}-${timestamp}.png`,
      fullPage: true 
    });
  }

  /**
   * Clear browser storage
   */
  async clearStorage() {
    try {
      await this.page.evaluate(() => {
        localStorage.clear();
        sessionStorage.clear();
      });
    } catch (error) {
      // If storage is blocked, try to navigate to a proper URL first
      if (error.message.includes('Access is denied') || error.message.includes('SecurityError')) {
        console.warn('Storage access denied, trying to navigate to proper origin first...');

        const currentUrl = this.page.url();
        const baseUrl = process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:4173';

        // If we're not on a proper HTTP URL, navigate to one
        if (!currentUrl.startsWith('http://') && !currentUrl.startsWith('https://')) {
          await this.page.goto(baseUrl);
          await this.waitForPageLoad();
        }

        // Try again after navigation
        await this.page.evaluate(() => {
          localStorage.clear();
          sessionStorage.clear();
        });
      } else {
        throw error;
      }
    }
  }

  /**
   * Set localStorage item
   */
  async setLocalStorage(key: string, value: string) {
    try {
      await this.page.evaluate(
        ({ key, value }) => localStorage.setItem(key, value),
        { key, value }
      );
    } catch (error) {
      // If localStorage is blocked, try to navigate to a proper URL first
      if (error.message.includes('Access is denied') || error.message.includes('SecurityError')) {
        console.warn('localStorage access denied, trying to navigate to proper origin first...');

        // Get the base URL from the page or use a default
        const currentUrl = this.page.url();
        const baseUrl = process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:4173';

        // If we're not on a proper HTTP URL, navigate to one
        if (!currentUrl.startsWith('http://') && !currentUrl.startsWith('https://')) {
          await this.page.goto(baseUrl);
          await this.waitForPageLoad();
        }

        // Try again after navigation
        await this.page.evaluate(
          ({ key, value }) => localStorage.setItem(key, value),
          { key, value }
        );
      } else {
        throw error;
      }
    }
  }

  /**
   * Get localStorage item
   */
  async getLocalStorage(key: string): Promise<string | null> {
    try {
      return await this.page.evaluate(
        (key) => localStorage.getItem(key),
        key
      );
    } catch (error) {
      // If localStorage is blocked, try to navigate to a proper URL first
      if (error.message.includes('Access is denied') || error.message.includes('SecurityError')) {
        console.warn('localStorage access denied, trying to navigate to proper origin first...');

        const currentUrl = this.page.url();
        const baseUrl = process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:4173';

        // If we're not on a proper HTTP URL, navigate to one
        if (!currentUrl.startsWith('http://') && !currentUrl.startsWith('https://')) {
          await this.page.goto(baseUrl);
          await this.waitForPageLoad();
        }

        // Try again after navigation
        return await this.page.evaluate(
          (key) => localStorage.getItem(key),
          key
        );
      } else {
        throw error;
      }
    }
  }

  /**
   * Wait for API response
   */
  async waitForApiResponse(urlPattern: string | RegExp, timeout = 10000) {
    return await this.page.waitForResponse(
      response => {
        const url = response.url();
        if (typeof urlPattern === 'string') {
          return url.includes(urlPattern);
        }
        return urlPattern.test(url);
      },
      { timeout }
    );
  }

  /**
   * Mock API response
   */
  async mockApiResponse(urlPattern: string | RegExp, responseData: any, status = 200) {
    await this.page.route(urlPattern, route => {
      route.fulfill({
        status,
        contentType: 'application/json',
        body: JSON.stringify(responseData)
      });
    });
  }

  /**
   * Mock API error response that should trigger toast notifications
   */
  async mockApiError(url: string, status = 500, message = 'Internal Server Error') {
    await this.page.route(url, (route) => {
      route.fulfill({
        status,
        contentType: 'application/json',
        body: JSON.stringify({
          error: message,
          message: message
        })
      });
    });
  }

  /**
   * Mock network timeout/failure
   */
  async mockNetworkFailure(url: string) {
    await this.page.route(url, (route) => {
      route.abort('failed');
    });
  }

  /**
   * Inject toast notification for testing
   */
  async injectToast(message: string, type: 'success' | 'error' | 'info' = 'info') {
    await this.page.evaluate(({ message, type }) => {
      // Create a toast element that matches the expected selectors
      const toast = document.createElement('div');
      toast.setAttribute('data-testid', 'toast');
      toast.setAttribute('role', 'alert');
      toast.className = `sonner-toast toast-${type}`;
      toast.textContent = message;
      toast.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999; background: white; border: 1px solid #ccc; padding: 12px; border-radius: 4px;';

      document.body.appendChild(toast);

      // Remove after 3 seconds
      setTimeout(() => {
        if (toast.parentNode) {
          toast.parentNode.removeChild(toast);
        }
      }, 3000);
    }, { message, type });
  }
}

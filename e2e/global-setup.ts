import { FullConfig } from '@playwright/test';
import { BackendService } from './services/backend-service';
import * as fs from 'fs';
import * as path from 'path';

let backendService: BackendService;

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global E2E test setup...');

  try {
    // Initialize backend service
    backendService = new BackendService();

    // Start backend service (database + API)
    const { apiUrl, dbUrl } = await backendService.start();

    // Save service URLs for tests to use
    const serviceConfig = {
      apiUrl,
      dbUrl,
      timestamp: new Date().toISOString()
    };

    // Create test-results directory if it doesn't exist
    const testResultsDir = path.join(process.cwd(), 'test-results');
    if (!fs.existsSync(testResultsDir)) {
      fs.mkdirSync(testResultsDir, { recursive: true });
    }

    // Save service configuration
    fs.writeFileSync(
      path.join(testResultsDir, 'service-config.json'),
      JSON.stringify(serviceConfig, null, 2)
    );

    // Update environment variables for tests
    // Keep PLAYWRIGHT_BASE_URL for frontend, set TEST_API_URL for API calls
    process.env.TEST_API_URL = apiUrl;
    process.env.TEST_DB_URL = dbUrl;

    // Frontend should still use the configured frontend URL
    if (!process.env.PLAYWRIGHT_BASE_URL || process.env.PLAYWRIGHT_BASE_URL === apiUrl) {
      process.env.PLAYWRIGHT_BASE_URL = 'http://localhost:4173';
    }

    // Wait a bit for services to be fully ready
    console.log('⏳ Waiting for services to be fully ready...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Verify services are healthy
    const isHealthy = await backendService.healthCheck();
    if (!isHealthy) {
      throw new Error('Backend service health check failed');
    }

    console.log('✅ Global setup completed successfully');
    console.log(`🌐 API URL: ${apiUrl}`);
    console.log(`🗄️ Database URL: ${dbUrl}`);

  } catch (error) {
    console.error('❌ Global setup failed:', error);

    // Cleanup on failure
    if (backendService) {
      await backendService.stop();
    }

    throw error;
  }
}

export default globalSetup;

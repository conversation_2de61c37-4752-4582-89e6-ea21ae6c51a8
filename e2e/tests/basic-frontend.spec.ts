import { test, expect } from '@playwright/test';

test.describe('Basic Frontend Tests', () => {
  test('should load the frontend application', async ({ page }) => {
    // Navigate to the frontend application
    await page.goto('/');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Should redirect to login page when not authenticated
    await expect(page).toHaveURL(/.*\/login/);
    
    // Should have the correct title
    await expect(page).toHaveTitle(/Painel do Parceiro/);
    
    // Should show login form
    await expect(page.locator('input[type="email"]')).toBeVisible();
    
    console.log('✅ Frontend application loaded successfully');
  });

  test('should have responsive design', async ({ page }) => {
    await page.goto('/login');
    
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForLoadState('networkidle');
    
    // Login form should still be visible
    await expect(page.locator('input[type="email"]')).toBeVisible();
    
    // Test desktop viewport
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.waitForLoadState('networkidle');
    
    await expect(page.locator('input[type="email"]')).toBeVisible();
    
    console.log('✅ Responsive design working correctly');
  });

  test('should validate email format', async ({ page }) => {
    await page.goto('/login');
    await page.waitForLoadState('networkidle');
    
    // Try invalid email
    await page.fill('input[type="email"]', 'invalid-email');
    await page.click('button:has-text("Enviar Código")');
    
    // Should show validation error or prevent submission
    const emailInput = page.locator('input[type="email"]');
    const isInvalid = await emailInput.evaluate((input: HTMLInputElement) => {
      return !input.validity.valid;
    });
    
    expect(isInvalid).toBeTruthy();
    
    console.log('✅ Email validation working correctly');
  });
});

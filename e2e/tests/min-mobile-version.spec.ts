import { test, expect } from '@playwright/test';
import { AuthHelpers } from '../utils/auth-helpers';
import { TestHelpers } from '../utils/test-helpers';

test.describe('Min Mobile Version Management', () => {
  let authHelpers: AuthHelpers;
  let testHelpers: TestHelpers;

  test.beforeEach(async ({ page }) => {
    authHelpers = new AuthHelpers(page);
    testHelpers = new TestHelpers(page);
    
    // Set up admin authentication
    await testHelpers.mockApiResponse('/v1/user/me', {
      data: {
        external_id: 'admin-123',
        email: '<EMAIL>',
        role: 'admin'
      }
    });
    
    await testHelpers.mockApiResponse('/v1/company/my-companies', {
      data: {
        companies: [],
        dashboard_url: '/admin/dashboard'
      }
    });
    
    await authHelpers.mockAuthState({
      email: '<EMAIL>',
      role: 'admin',
      loginCode: '123456'
    });
  });

  test.describe('Feature Toggle Functionality', () => {
    test('should display toggle switch and handle activation/deactivation', async ({ page }) => {
      // Mock initial state - functionality disabled
      await testHelpers.mockApiResponse('/v1/min-mobile-version', {
        data: {
          ios: '',
          android: '',
          is_active: false
        }
      });

      // Mock activation endpoint
      await testHelpers.mockApiResponse('/v1/min-mobile-version/activation/true', {
        data: { message: 'Functionality activated successfully' }
      });

      // Mock deactivation endpoint
      await testHelpers.mockApiResponse('/v1/min-mobile-version/activation/false', {
        data: { message: 'Functionality deactivated successfully' }
      });

      await page.goto('/admin/min-mobile-version');
      await testHelpers.waitForPageLoad();

      // Verify page loads correctly
      await expect(page).toHaveURL(/.*\/admin\/min-mobile-version/);
      await expect(page.locator('h2:has-text("Versão Mínima Mobile")')).toBeVisible();

      // Verify toggle switch is present and shows disabled state
      const toggleSwitch = page.locator('#activation-toggle');
      await expect(toggleSwitch).toBeVisible();
      await expect(toggleSwitch).not.toBeChecked();

      // Verify disabled state UI elements
      await expect(page.locator('text=Desativado')).toBeVisible();
      await expect(page.locator('text=Funcionalidade inativa')).toBeVisible();
      await expect(page.locator('text=Funcionalidade desativada - ative para gerenciar versões mínimas')).toBeVisible();

      // Verify disabled functionality message is shown
      await expect(page.locator('text=Funcionalidade Desativada')).toBeVisible();
      await expect(page.locator('text=A funcionalidade de versão mínima mobile está atualmente desativada')).toBeVisible();

      // Verify form controls are hidden when disabled
      await expect(page.locator('input[id="ios"]')).not.toBeVisible();
      await expect(page.locator('input[id="android"]')).not.toBeVisible();

      console.log('✅ Disabled state displayed correctly');
    });

    test('should activate functionality and show form controls', async ({ page }) => {
      // Mock initial disabled state
      await testHelpers.mockApiResponse('/v1/min-mobile-version', {
        data: {
          ios: '',
          android: '',
          is_active: false
        }
      });

      // Mock activation endpoint
      await testHelpers.mockApiResponse('/v1/min-mobile-version/activation/true', {
        data: { message: 'Functionality activated successfully' }
      });

      // Mock activated state response (for refetch after activation)
      await testHelpers.mockApiResponse('/v1/min-mobile-version', {
        data: {
          ios: '1.0.0',
          android: '1.0.0',
          is_active: true
        }
      });

      await page.goto('/admin/min-mobile-version');
      await testHelpers.waitForPageLoad();

      // Click toggle to activate
      const toggleSwitch = page.locator('#activation-toggle');
      await toggleSwitch.click();

      // Wait for API call and state update
      await page.waitForTimeout(1000);

      // Verify success toast message
      await testHelpers.waitForToast('Funcionalidade de versão mínima ativada com sucesso!');

      // Verify toggle switch shows enabled state
      await expect(toggleSwitch).toBeChecked();
      await expect(page.locator('text=Ativado')).toBeVisible();
      await expect(page.locator('text=Funcionalidade ativa')).toBeVisible();

      // Verify form controls are now visible
      await expect(page.locator('input[id="ios"]')).toBeVisible();
      await expect(page.locator('input[id="android"]')).toBeVisible();

      // Verify current versions are displayed
      await expect(page.locator('text=Versão Atual iOS')).toBeVisible();
      await expect(page.locator('text=Versão Atual Android')).toBeVisible();

      // Verify disabled functionality message is hidden
      await expect(page.locator('text=Funcionalidade Desativada')).not.toBeVisible();

      console.log('✅ Activation functionality working correctly');
    });

    test('should deactivate functionality and hide form controls', async ({ page }) => {
      // Mock initial enabled state
      await testHelpers.mockApiResponse('/v1/min-mobile-version', {
        data: {
          ios: '1.2.0',
          android: '1.1.0',
          is_active: true
        }
      });

      // Mock deactivation endpoint
      await testHelpers.mockApiResponse('/v1/min-mobile-version/activation/false', {
        data: { message: 'Functionality deactivated successfully' }
      });

      // Mock deactivated state response (for refetch after deactivation)
      await testHelpers.mockApiResponse('/v1/min-mobile-version', {
        data: {
          ios: '',
          android: '',
          is_active: false
        }
      });

      await page.goto('/admin/min-mobile-version');
      await testHelpers.waitForPageLoad();

      // Verify initial enabled state
      const toggleSwitch = page.locator('#activation-toggle');
      await expect(toggleSwitch).toBeChecked();
      await expect(page.locator('input[id="ios"]')).toBeVisible();

      // Click toggle to deactivate
      await toggleSwitch.click();

      // Wait for API call and state update
      await page.waitForTimeout(1000);

      // Verify success toast message
      await testHelpers.waitForToast('Funcionalidade de versão mínima desativada com sucesso!');

      // Verify toggle switch shows disabled state
      await expect(toggleSwitch).not.toBeChecked();
      await expect(page.locator('text=Desativado')).toBeVisible();

      // Verify form controls are hidden
      await expect(page.locator('input[id="ios"]')).not.toBeVisible();
      await expect(page.locator('input[id="android"]')).not.toBeVisible();

      // Verify disabled functionality message is shown
      await expect(page.locator('text=Funcionalidade Desativada')).toBeVisible();

      console.log('✅ Deactivation functionality working correctly');
    });
  });

  test.describe('Version Management When Active', () => {
    test('should manage versions when functionality is active', async ({ page }) => {
      // Mock enabled state with existing versions
      await testHelpers.mockApiResponse('/v1/min-mobile-version', {
        data: {
          ios: '1.2.0',
          android: '1.1.0',
          is_active: true
        }
      });

      // Mock version update endpoint
      await testHelpers.mockApiResponse('/v1/min-mobile-version', {
        data: { message: 'Versions updated successfully' }
      }, 201);

      await page.goto('/admin/min-mobile-version');
      await testHelpers.waitForPageLoad();

      // Verify form is visible and populated
      const iosInput = page.locator('input[id="ios"]');
      const androidInput = page.locator('input[id="android"]');
      
      await expect(iosInput).toBeVisible();
      await expect(androidInput).toBeVisible();
      await expect(iosInput).toHaveValue('1.2.0');
      await expect(androidInput).toHaveValue('1.1.0');

      // Update versions
      await iosInput.clear();
      await iosInput.fill('1.3.0');
      await androidInput.clear();
      await androidInput.fill('1.2.0');

      // Submit form
      await testHelpers.clickAndWait('button[type="submit"]:has-text("ATUALIZAR")');

      // Verify success message
      await testHelpers.waitForToast('Versões mínimas atualizadas com sucesso!');

      console.log('✅ Version management working correctly when active');
    });

    test('should handle first-time setup when active with empty versions', async ({ page }) => {
      // Mock enabled state with empty versions (first-time setup)
      await testHelpers.mockApiResponse('/v1/min-mobile-version', {
        data: {
          ios: '',
          android: '',
          is_active: true
        }
      });

      // Mock version creation endpoint
      await testHelpers.mockApiResponse('/v1/min-mobile-version', {
        data: { message: 'Versions configured successfully' }
      }, 201);

      await page.goto('/admin/min-mobile-version');
      await testHelpers.waitForPageLoad();

      // Verify first-time setup UI
      await expect(page.locator('text=Primeira configuração - defina as versões mínimas iniciais')).toBeVisible();
      await expect(page.locator('text=Configurar Versões Mínimas Iniciais')).toBeVisible();
      await expect(page.locator('text=Primeira configuração:')).toBeVisible();

      // Verify default values are set
      const iosInput = page.locator('input[id="ios"]');
      const androidInput = page.locator('input[id="android"]');
      
      await expect(iosInput).toHaveValue('1.0.0');
      await expect(androidInput).toHaveValue('1.0.0');

      // Update and submit
      await iosInput.clear();
      await iosInput.fill('1.1.0');
      await androidInput.clear();
      await androidInput.fill('1.0.5');

      await testHelpers.clickAndWait('button[type="submit"]:has-text("CONFIGURAR")');

      // Verify success message for first-time setup
      await testHelpers.waitForToast('Versões mínimas configuradas com sucesso!');

      console.log('✅ First-time setup working correctly');
    });
  });

  test.describe('Form Validation', () => {
    test('should validate version format', async ({ page }) => {
      // Mock enabled state
      await testHelpers.mockApiResponse('/v1/min-mobile-version', {
        data: {
          ios: '1.0.0',
          android: '1.0.0',
          is_active: true
        }
      });

      await page.goto('/admin/min-mobile-version');
      await testHelpers.waitForPageLoad();

      // Test invalid version formats
      const iosInput = page.locator('input[id="ios"]');
      const androidInput = page.locator('input[id="android"]');

      // Test invalid iOS version
      await iosInput.clear();
      await iosInput.fill('1.0');
      await iosInput.blur();

      // Verify validation error
      await expect(page.locator('text=Formato inválido. Use o formato X.Y.Z (ex: 1.2.3)')).toBeVisible();

      // Test invalid Android version
      await androidInput.clear();
      await androidInput.fill('invalid');
      await androidInput.blur();

      // Verify validation error
      await expect(page.locator('text=Formato inválido. Use o formato X.Y.Z (ex: 1.2.3)')).toBeVisible();

      // Verify submit button is disabled with validation errors
      const submitButton = page.locator('button[type="submit"]');
      await expect(submitButton).toBeDisabled();

      // Fix validation errors
      await iosInput.clear();
      await iosInput.fill('1.2.0');
      await androidInput.clear();
      await androidInput.fill('1.1.0');

      // Verify submit button is enabled
      await expect(submitButton).toBeEnabled();

      console.log('✅ Form validation working correctly');
    });
  });

  test.describe('Navigation', () => {
    test('should be accessible from admin navigation', async ({ page }) => {
      // Mock initial state
      await testHelpers.mockApiResponse('/v1/min-mobile-version', {
        data: {
          ios: '1.0.0',
          android: '1.0.0',
          is_active: true
        }
      });

      // Start from admin dashboard
      await page.goto('/admin/dashboard');
      await testHelpers.waitForPageLoad();

      // Navigate to min-mobile-version page via sidebar
      await testHelpers.clickAndWait('nav a[href="/admin/min-mobile-version"]');

      // Verify navigation worked
      await expect(page).toHaveURL(/.*\/admin\/min-mobile-version/);
      await expect(page.locator('h2:has-text("Versão Mínima Mobile")')).toBeVisible();

      // Verify navigation item is highlighted/active
      await expect(page.locator('nav a[href="/admin/min-mobile-version"]')).toHaveClass(/bg-primary/);

      console.log('✅ Navigation working correctly');
    });
  });

  test.describe('Error Handling', () => {
    test('should handle toggle activation errors gracefully', async ({ page }) => {
      // Mock initial disabled state
      await testHelpers.mockApiResponse('/v1/min-mobile-version', {
        data: {
          ios: '',
          android: '',
          is_active: false
        }
      });

      // Mock activation endpoint with error
      await testHelpers.mockApiResponse('/v1/min-mobile-version/activation/true', {
        error: { message: 'Failed to activate functionality' }
      }, 500);

      await page.goto('/admin/min-mobile-version');
      await testHelpers.waitForPageLoad();

      // Try to activate
      const toggleSwitch = page.locator('#activation-toggle');
      await toggleSwitch.click();

      // Wait for error response
      await page.waitForTimeout(1000);

      // Verify error toast message
      await testHelpers.waitForToast('Erro ao alterar status da funcionalidade');

      // Verify toggle remains in disabled state
      await expect(toggleSwitch).not.toBeChecked();

      console.log('✅ Error handling working correctly');
    });

    test('should handle version update errors gracefully', async ({ page }) => {
      // Mock enabled state
      await testHelpers.mockApiResponse('/v1/min-mobile-version', {
        data: {
          ios: '1.0.0',
          android: '1.0.0',
          is_active: true
        }
      });

      // Mock version update endpoint with error
      await testHelpers.mockApiResponse('/v1/min-mobile-version', {
        error: { message: 'Failed to update versions' }
      }, 500);

      await page.goto('/admin/min-mobile-version');
      await testHelpers.waitForPageLoad();

      // Update versions
      const iosInput = page.locator('input[id="ios"]');
      await iosInput.clear();
      await iosInput.fill('1.1.0');

      // Submit form
      await testHelpers.clickAndWait('button[type="submit"]:has-text("ATUALIZAR")');

      // Verify error toast message
      await testHelpers.waitForToast('Erro ao atualizar versões mínimas');

      console.log('✅ Version update error handling working correctly');
    });
  });
});

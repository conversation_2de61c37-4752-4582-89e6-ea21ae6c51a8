import { test, expect } from '@playwright/test';

test.describe('E2E Setup Verification', () => {
  test('should verify <PERSON><PERSON> is working', async ({ page }) => {
    // Navigate to a simple page to test basic functionality
    await page.goto('data:text/html,<html><body><h1>Test Page</h1></body></html>');
    
    // Check that we can interact with the page
    await expect(page.locator('h1')).toHaveText('Test Page');
    
    console.log('✅ <PERSON><PERSON> is working correctly');
  });

  test('should verify browser capabilities', async ({ page, browserName }) => {
    console.log(`🌐 Testing with browser: ${browserName}`);
    
    // Test basic browser functionality
    await page.goto('data:text/html,<html><body><div id="test">Hello World</div></body></html>');
    
    // Test element selection
    const element = page.locator('#test');
    await expect(element).toBeVisible();
    await expect(element).toHaveText('Hello World');
    
    // Test JavaScript execution
    const result = await page.evaluate(() => {
      return document.getElementById('test')?.textContent;
    });
    
    expect(result).toBe('Hello World');
    
    console.log(`✅ Browser ${browserName} is working correctly`);
  });

  test('should verify test helpers', async ({ page }) => {
    // Test our helper utilities
    const { TestHelpers } = await import('../utils/test-helpers');
    const helpers = new TestHelpers(page);
    
    // Create a simple test page
    await page.goto('data:text/html,<html><body><input id="test-input" type="text"><button id="test-button">Click me</button></body></html>');
    
    // Test fillField helper
    await helpers.fillField('#test-input', 'test value');
    await expect(page.locator('#test-input')).toHaveValue('test value');
    
    // Test waitForElement helper
    const button = await helpers.waitForElement('#test-button');
    await expect(button).toBeVisible();
    
    console.log('✅ Test helpers are working correctly');
  });

  test('should verify auth helpers', async ({ page }) => {
    const { AuthHelpers } = await import('../utils/auth-helpers');
    const authHelpers = new AuthHelpers(page);

    // Create a simple HTTP server response to allow localStorage
    await page.route('http://localhost:3000/test', route => {
      route.fulfill({
        status: 200,
        contentType: 'text/html',
        body: '<html><body><div>Test</div></body></html>'
      });
    });

    await page.goto('http://localhost:3000/test');

    // Test authentication state check
    const isAuth = await authHelpers.isAuthenticated();
    expect(isAuth).toBe(false);

    console.log('✅ Auth helpers are working correctly');
  });

  test('should verify environment configuration', async () => {
    // Check that environment variables are accessible
    const baseUrl = process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:8080';
    expect(baseUrl).toBeTruthy();
    
    console.log(`✅ Base URL configured: ${baseUrl}`);
    
    // Check test environment variables
    const testAdminEmail = process.env.TEST_ADMIN_EMAIL || '<EMAIL>';
    const testPartnerEmail = process.env.TEST_PARTNER_EMAIL || '<EMAIL>';
    
    expect(testAdminEmail).toBeTruthy();
    expect(testPartnerEmail).toBeTruthy();
    
    console.log('✅ Environment configuration is working correctly');
  });
});

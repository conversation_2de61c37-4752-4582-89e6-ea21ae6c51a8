import { test, expect } from '@playwright/test';

test.describe('Basic Smoke Tests', () => {
  test('should load the application', async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    
    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Should have the correct title
    await expect(page).toHaveTitle(/Painel do Parceiro/);
    
    // Should redirect to login page when not authenticated
    await expect(page).toHaveURL(/.*\/login/);
    
    // Should show login form
    await expect(page.locator('input[type="email"]')).toBeVisible();
  });

  test('should have correct meta tags', async ({ page }) => {
    await page.goto('/login');
    
    // Check meta tags
    const description = await page.locator('meta[name="description"]').getAttribute('content');
    expect(description).toContain('Painel do Parceiro');
    
    const viewport = await page.locator('meta[name="viewport"]').getAttribute('content');
    expect(viewport).toContain('width=device-width');
  });

  test('should load CSS and fonts', async ({ page }) => {
    await page.goto('/login');
    await page.waitForLoadState('networkidle');
    
    // Check if fonts are loaded
    const fontFamily = await page.evaluate(() => {
      return window.getComputedStyle(document.body).fontFamily;
    });
    
    expect(fontFamily).toContain('Inter');
  });

  test('should be responsive', async ({ page }) => {
    await page.goto('/login');
    
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForLoadState('networkidle');
    
    // Login form should still be visible
    await expect(page.locator('input[type="email"]')).toBeVisible();
    
    // Test tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.waitForLoadState('networkidle');
    
    await expect(page.locator('input[type="email"]')).toBeVisible();
    
    // Test desktop viewport
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.waitForLoadState('networkidle');
    
    await expect(page.locator('input[type="email"]')).toBeVisible();
  });
});

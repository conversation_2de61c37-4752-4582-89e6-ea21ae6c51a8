import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

/**
 * Backend Service Manager using Docker Compose
 * This manages the backend API service and database for E2E testing
 */
export class BackendService {
  private apiUrl: string = '';
  private dbUrl: string = '';
  private isRunning: boolean = false;

  /**
   * Start the backend service with database using Docker Compose
   */
  async start(): Promise<{ apiUrl: string; dbUrl: string }> {
    console.log('🚀 Starting backend service with Docker Compose...');

    try {
      // Stop any existing services
      await this.stop();

      // Determine which service to start
      const { service, composeFile } = await this.determineBackendService();

      // Start services with Docker Compose
      await this.startDockerComposeServices(service, composeFile);

      // Wait for services to be ready
      await this.waitForServices();

      console.log('✅ Backend service started successfully');
      console.log(`📊 Database URL: ${this.dbUrl}`);
      console.log(`🌐 API URL: ${this.apiUrl}`);

      this.isRunning = true;

      return {
        apiUrl: this.apiUrl,
        dbUrl: this.dbUrl
      };
    } catch (error) {
      console.error('❌ Failed to start backend service:', error);
      await this.stop();
      throw error;
    }
  }

  /**
   * Determine which backend service to use and which compose file
   */
  private async determineBackendService(): Promise<{ service: string; composeFile: string }> {
    // Check if mock API is forced
    if (process.env.FORCE_MOCK_API === 'true') {
      console.log('🎭 Mock API forced via FORCE_MOCK_API - using mock API service');
      return { service: 'mock-api', composeFile: 'docker-compose.local.yml' };
    }

    // Check if we're in CI environment
    const isCI = process.env.CI === 'true';

    // Check if local backend directory exists
    const fs = await import('fs');
    const path = await import('path');
    const localBackendPath = path.resolve('../backend');
    const hasLocalBackend = fs.existsSync(localBackendPath);

    if (isCI) {
      console.log('🔄 CI environment detected - using Git repository build');
      return { service: 'test-api', composeFile: 'docker-compose.ci.yml' };
    }

    if (hasLocalBackend) {
      console.log('🏠 Local backend directory found - using local build');
      return { service: 'test-api', composeFile: 'docker-compose.local.yml' };
    }

    console.log('🎭 No backend available - using mock API service');
    return { service: 'mock-api', composeFile: 'docker-compose.local.yml' };
  }

  /**
   * Start backend services (Docker Compose or fallback)
   */
  private async startDockerComposeServices(serviceName: string, composeFile: string): Promise<void> {
    console.log(`🚀 Starting backend services: ${serviceName} using ${composeFile}...`);

    if (serviceName === 'mock-api') {
      // Start mock API directly with Node.js
      await this.startMockApiDirectly();
      this.setServiceUrls('mock-api');
    } else {
      // Use Docker Compose for real backend services
      try {
        await this.startRealBackendServices(composeFile);
        this.setServiceUrls('test-api');
      } catch (error) {
        // If real backend fails, the startRealBackendServices method will handle fallback
        this.setServiceUrls('mock-api');
      }
    }
  }

  /**
   * Start mock API directly with Node.js (fallback)
   */
  private async startMockApiDirectly(): Promise<void> {
    console.log('🎭 Starting mock API directly with Node.js...');

    try {
      // Install dependencies
      console.log('📦 Installing mock API dependencies...');
      await execAsync('cd e2e/mock-api && npm install');

      // Start the mock API in background
      console.log('🚀 Starting mock API server...');
      exec('cd e2e/mock-api && PORT=8082 npm start');

      // Give it a moment to start
      await new Promise(resolve => setTimeout(resolve, 2000));

      console.log('✅ Mock API started directly on port 8082');
    } catch (error) {
      console.error('❌ Failed to start mock API directly:', error);
      throw error;
    }
  }

  /**
   * Start real backend services with Docker Compose
   */
  private async startRealBackendServices(composeFile: string): Promise<void> {
    console.log(`🐳 Starting real backend services with ${composeFile}...`);

    // Set environment variables for Docker Compose
    const env = {
      ...process.env,
      BACKEND_REPO_URL: process.env.BACKEND_REPO_URL || 'https://github.com/izy-mercado/backend.git',
      BACKEND_BRANCH: process.env.BACKEND_BRANCH || 'main'
    };

    try {
      await execAsync(`docker-compose -f ${composeFile} up -d test-db test-api`, { env });
      console.log(`✅ Docker Compose services started: test-db test-api (using ${composeFile})`);
    } catch (error) {
      console.error(`❌ Failed to start Docker Compose services with ${composeFile}:`, error);
      console.log('🎭 Falling back to mock API service...');

      // Clean up any partially started services
      await execAsync(`docker-compose -f ${composeFile} down -v`).catch(() => {});

      // Start mock API instead
      await this.startMockApiDirectly();
      return;
    }
  }

  /**
   * Set service URLs based on the service type
   */
  private setServiceUrls(serviceName: string): void {
    if (serviceName === 'mock-api') {
      this.apiUrl = 'http://localhost:8082';
      this.dbUrl = ''; // Mock API doesn't need database
    } else {
      this.apiUrl = 'http://localhost:8081';
      this.dbUrl = 'postgres://postgres:postgres@localhost:5434/izymercado_test';
    }
  }

  /**
   * Wait for services to be ready
   */
  private async waitForServices(): Promise<void> {
    console.log('⏳ Waiting for services to be ready...');
    console.log(`🌐 Testing API URL: ${this.apiUrl}`);

    // For real backend, give more time as it needs to build and run migrations
    const isRealBackend = this.apiUrl.includes('8081');
    const maxRetries = isRealBackend ? 60 : 30; // 2 minutes for real backend, 1 minute for mock
    const retryDelay = 2000; // 2 seconds

    if (isRealBackend) {
      console.log('🏗️ Real backend detected - allowing extra time for build and migrations...');
    }

    for (let i = 0; i < maxRetries; i++) {
      try {
        console.log(`🔍 Attempt ${i + 1}/${maxRetries}: Testing ${this.apiUrl}/health`);
        const response = await fetch(`${this.apiUrl}/health`);
        if (response.ok) {
          const healthData = await response.json();
          console.log('✅ API service is ready:', healthData);
          return;
        } else {
          console.log(`⚠️ API responded with status: ${response.status}`);
        }
      } catch (error) {
        console.log(`❌ API request failed: ${error.message}`);
      }

      // Check if we're using Docker Compose and the container has failed
      // Only check for failures after giving it some time to start
      if (this.apiUrl.includes('8081') && i > 10) { // Real backend port, after 20 seconds
        try {
          const { stdout } = await execAsync('docker-compose -f docker-compose.local.yml ps test-api');
          console.log('🐳 Container status:', stdout.trim());

          if (stdout.includes('Exit ')) {
            console.log('🔄 API container failed, checking logs...');

            // Show recent logs
            try {
              const { stdout: logs } = await execAsync('docker-compose -f docker-compose.local.yml logs --tail=20 test-api');
              console.log('📋 Recent API logs:');
              console.log(logs);
            } catch (logError) {
              console.log('❌ Could not fetch logs:', logError.message);
            }

            console.log('🎭 Falling back to mock API...');
            await this.fallbackToMockApi();
            return;
          }
        } catch (error) {
          console.log('⚠️ Could not check container status:', error.message);
        }
      }

      console.log(`⏳ Waiting for API service... (${i + 1}/${maxRetries})`);
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }

    // Before throwing error, show final diagnostics
    console.log('❌ API service failed to become ready. Final diagnostics:');

    if (this.apiUrl.includes('8081')) {
      try {
        const { stdout } = await execAsync('docker-compose -f docker-compose.local.yml ps');
        console.log('🐳 All container statuses:');
        console.log(stdout);

        const { stdout: logs } = await execAsync('docker-compose -f docker-compose.local.yml logs --tail=20 test-api');
        console.log('📋 Final API logs:');
        console.log(logs);
      } catch (error) {
        console.log('❌ Could not fetch final diagnostics:', error.message);
      }
    }

    throw new Error('API service failed to become ready within timeout');
  }

  /**
   * Fallback to mock API when real backend fails
   */
  private async fallbackToMockApi(): Promise<void> {
    console.log('🎭 Starting fallback to mock API...');

    // Clean up failed Docker services
    await execAsync('docker-compose -f docker-compose.local.yml down -v').catch(() => {});

    // Start mock API
    await this.startMockApiDirectly();
    this.setServiceUrls('mock-api');

    // Wait for mock API to be ready
    const maxRetries = 10;
    const retryDelay = 1000;

    for (let i = 0; i < maxRetries; i++) {
      try {
        const response = await fetch(`${this.apiUrl}/health`);
        if (response.ok) {
          console.log('✅ Mock API service is ready');
          return;
        }
      } catch (error) {
        // Continue waiting
      }

      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }

    throw new Error('Mock API failed to start');
  }

  /**
   * Stop all services
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    console.log('🛑 Stopping backend services...');

    try {
      // Stop Docker Compose services (try both compose files)
      await execAsync('docker-compose -f docker-compose.local.yml down -v').catch(() => {
        // Ignore errors if no services are running
      });

      await execAsync('docker-compose -f docker-compose.ci.yml down -v').catch(() => {
        // Ignore errors if no services are running
      });

      // Stop any Node.js processes on our ports
      await execAsync('pkill -f "PORT=8082 npm start"').catch(() => {
        // Ignore errors if no processes are running
      });

      console.log('✅ Backend services stopped');
    } catch (error) {
      console.error('❌ Error stopping backend services:', error);
    }

    this.isRunning = false;
    this.apiUrl = '';
    this.dbUrl = '';

    console.log('✅ Backend service cleanup completed');
  }

  /**
   * Get API URL
   */
  getApiUrl(): string {
    return this.apiUrl;
  }

  /**
   * Get Database URL
   */
  getDbUrl(): string {
    return this.dbUrl;
  }

  /**
   * Health check
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await fetch(`${this.apiUrl}/health`);
      return response.ok;
    } catch {
      return false;
    }
  }

}

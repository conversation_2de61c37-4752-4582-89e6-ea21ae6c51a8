/**
 * Utility functions for formatting data
 */

/**
 * Format CPF with mask (000.000.000-00)
 */
export const formatCPF = (cpf: string): string => {
  if (!cpf) return '';
  
  // Remove all non-numeric characters
  const cleanCPF = cpf.replace(/\D/g, '');
  
  // Check if it has the correct length
  if (cleanCPF.length !== 11) return cpf;
  
  // Apply the mask
  return cleanCPF.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
};

/**
 * Format CNPJ with mask (00.000.000/0000-00)
 */
export const formatCNPJ = (cnpj: string): string => {
  if (!cnpj) return '';
  
  // Remove all non-numeric characters
  const cleanCNPJ = cnpj.replace(/\D/g, '');
  
  // Check if it has the correct length
  if (cleanCNPJ.length !== 14) return cnpj;
  
  // Apply the mask
  return cleanCNPJ.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
};

/**
 * Format phone number with mask (+55 11 99999-9999)
 */
export const formatPhone = (phone: string): string => {
  if (!phone) return '';
  
  // Remove all non-numeric characters
  const cleanPhone = phone.replace(/\D/g, '');
  
  // Check if it starts with country code
  if (cleanPhone.startsWith('55') && cleanPhone.length === 13) {
    // Format: +55 11 99999-9999
    return cleanPhone.replace(/(\d{2})(\d{2})(\d{5})(\d{4})/, '+$1 $2 $3-$4');
  } else if (cleanPhone.length === 11) {
    // Format: (11) 99999-9999
    return cleanPhone.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
  } else if (cleanPhone.length === 10) {
    // Format: (11) 9999-9999
    return cleanPhone.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
  }
  
  return phone;
};

/**
 * Format currency in Brazilian Real (R$ 1.234,56)
 */
export const formatCurrency = (value: number): string => {
  // Handle NaN and invalid values
  console.log(value)
  if (isNaN(value) || value === null || value === undefined) {
    return 'R$ 0,00';
  }

  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(value);
};

/**
 * Format date in Brazilian format (dd/mm/yyyy)
 */
export const formatDate = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('pt-BR');
};

/**
 * Format date and time in Brazilian format (dd/mm/yyyy HH:mm)
 */
export const formatDateTime = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleString('pt-BR');
};

/**
 * Remove formatting from CPF (keep only numbers)
 */
export const cleanCPF = (cpf: string): string => {
  return cpf.replace(/\D/g, '');
};

/**
 * Remove formatting from CNPJ (keep only numbers)
 */
export const cleanCNPJ = (cnpj: string): string => {
  return cnpj.replace(/\D/g, '');
};

/**
 * Remove formatting from phone (keep only numbers)
 */
export const cleanPhone = (phone: string): string => {
  return phone.replace(/\D/g, '');
};

/**
 * Verifica se um produto é vendido por peso (kg)
 * @param productName Nome do produto
 * @returns true se o produto contém "(kg)" no nome
 */
export const isProductByWeight = (productName: string): boolean => {
  return productName.toLowerCase().includes('(kg)');
};

/**
 * Converte estoque para envio ao backend (multiplica por 10 se produto for por peso)
 * @param stock Valor do estoque informado pelo usuário
 * @param productName Nome do produto
 * @returns Estoque convertido para envio ao backend
 */
export const convertStockForBackend = (stock: number, productName: string): number => {
  if (isProductByWeight(productName)) {
    return stock * 10; // Cada unidade de estoque equivale a 100g
  }
  return stock;
};

/**
 * Converte estoque para exibição (divide por 10 se produto for por peso)
 * @param stock Valor do estoque vindo do backend
 * @param productName Nome do produto
 * @returns Estoque convertido para exibição
 */
export const convertStockForDisplay = (stock: number, productName: string): number => {
  if (isProductByWeight(productName)) {
    return stock / 10; // Converte de 100g para kg
  }
  return stock;
};

/**
 * Tests for search utilities
 */

import { removeAccents, fuzzyMatch, searchProducts } from '../searchUtils';

describe('searchUtils', () => {
  describe('removeAccents', () => {
    it('should remove accents from text', () => {
      expect(removeAccents('café')).toBe('cafe');
      expect(removeAccents('açúcar')).toBe('acucar');
      expect(removeAccents('coração')).toBe('coracao');
      expect(removeAccents('maçã')).toBe('maca');
      expect(removeAccents('pão')).toBe('pao');
    });

    it('should convert to lowercase', () => {
      expect(removeAccents('CAFÉ')).toBe('cafe');
      expect(removeAccents('Açúcar')).toBe('acucar');
    });
  });

  describe('fuzzyMatch', () => {
    it('should match exact strings (case insensitive)', () => {
      expect(fuzzyMatch('cafe', 'Café')).toBe(true);
      expect(fuzzyMatch('açúcar', 'AÇÚCAR')).toBe(true);
    });

    it('should match partial strings', () => {
      expect(fuzzyMatch('cafe', 'Café com leite')).toBe(true);
      expect(fuzzyMatch('leite', 'Café com leite')).toBe(true);
    });

    it('should handle accent-insensitive matching', () => {
      expect(fuzzyMatch('cafe', 'Café')).toBe(true);
      expect(fuzzyMatch('acucar', 'Açúcar')).toBe(true);
      expect(fuzzyMatch('pao', 'Pão de açúcar')).toBe(true);
    });

    it('should return true for empty search terms', () => {
      expect(fuzzyMatch('', 'any text')).toBe(true);
      expect(fuzzyMatch('   ', 'any text')).toBe(true);
    });

    it('should handle fuzzy matching with similarity threshold', () => {
      // These should match with default threshold (0.6)
      expect(fuzzyMatch('cafe', 'caffe')).toBe(true);
      expect(fuzzyMatch('acucar', 'açucar')).toBe(true);
      
      // These should not match (too different)
      expect(fuzzyMatch('cafe', 'chocolate')).toBe(false);
    });
  });

  describe('searchProducts', () => {
    const mockProducts = [
      {
        external_id: '1',
        name: 'Café Premium',
        brand: 'Marca A',
        ean: '1234567890123',
        image: 'image1.jpg',
        is_reviewed: true,
        is_18_plus: false,
        is_active: true,
        categories: []
      },
      {
        external_id: '2',
        name: 'Açúcar Cristal',
        brand: 'Marca B',
        ean: '2345678901234',
        image: 'image2.jpg',
        is_reviewed: true,
        is_18_plus: false,
        is_active: true,
        categories: []
      },
      {
        external_id: '3',
        name: 'Leite Integral',
        brand: 'Marca C',
        ean: '3456789012345',
        image: 'image3.jpg',
        is_reviewed: true,
        is_18_plus: false,
        is_active: true,
        categories: []
      }
    ];

    it('should return all products for empty search term', () => {
      const result = searchProducts(mockProducts, '');
      expect(result).toHaveLength(3);
    });

    it('should search by product name', () => {
      const result = searchProducts(mockProducts, 'cafe');
      expect(result).toHaveLength(1);
      expect(result[0].name).toBe('Café Premium');
    });

    it('should search by brand', () => {
      const result = searchProducts(mockProducts, 'Marca B');
      expect(result).toHaveLength(1);
      expect(result[0].brand).toBe('Marca B');
    });

    it('should search by EAN', () => {
      const result = searchProducts(mockProducts, '1234567890123');
      expect(result).toHaveLength(1);
      expect(result[0].ean).toBe('1234567890123');
    });

    it('should be accent-insensitive', () => {
      const result = searchProducts(mockProducts, 'acucar');
      expect(result).toHaveLength(1);
      expect(result[0].name).toBe('Açúcar Cristal');
    });

    it('should be case-insensitive', () => {
      const result = searchProducts(mockProducts, 'CAFÉ');
      expect(result).toHaveLength(1);
      expect(result[0].name).toBe('Café Premium');
    });

    it('should handle partial matches', () => {
      const result = searchProducts(mockProducts, 'leite');
      expect(result).toHaveLength(1);
      expect(result[0].name).toBe('Leite Integral');
    });

    it('should return empty array for no matches', () => {
      const result = searchProducts(mockProducts, 'chocolate');
      expect(result).toHaveLength(0);
    });
  });
});

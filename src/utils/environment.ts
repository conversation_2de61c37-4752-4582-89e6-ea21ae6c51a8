/**
 * Environment utilities for displaying environment information in the UI
 */

export interface EnvironmentInfo {
  name: string;
  displayName: string;
  color: string;
  bgColor: string;
  show: boolean;
}

/**
 * Get environment information based on VITE_NODE_ENV
 */
export const getEnvironmentInfo = (): EnvironmentInfo => {
  const env = import.meta.env.VITE_NODE_ENV || 'development';
  
  switch (env) {
    case 'development':
      return {
        name: 'development',
        displayName: 'DEV',
        color: 'text-blue-700',
        bgColor: 'bg-blue-100',
        show: true
      };
    
    case 'staging':
      return {
        name: 'staging',
        displayName: 'STAGING',
        color: 'text-orange-700',
        bgColor: 'bg-orange-100',
        show: true
      };
    
    case 'production':
      return {
        name: 'production',
        displayName: 'PROD',
        color: 'text-green-700',
        bgColor: 'bg-green-100',
        show: false // Don't show in production
      };
    
    default:
      return {
        name: env,
        displayName: env.toUpperCase(),
        color: 'text-gray-700',
        bgColor: 'bg-gray-100',
        show: true
      };
  }
};

/**
 * Environment badge component props
 */
export interface EnvironmentBadgeProps {
  className?: string;
  size?: 'sm' | 'md';
}

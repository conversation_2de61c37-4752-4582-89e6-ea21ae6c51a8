/**
 * Enhanced search utilities for product filtering
 * Provides fuzzy matching, accent-insensitive search, and debouncing
 */

import { useCallback, useRef, useState } from 'react';

/**
 * Remove accents and diacritical marks from text
 */
export const removeAccents = (text: string): string => {
  return text
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .toLowerCase();
};

/**
 * Calculate Levenshtein distance between two strings
 * Used for fuzzy matching
 */
export const levenshteinDistance = (str1: string, str2: string): number => {
  const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

  for (let i = 0; i <= str1.length; i++) {
    matrix[0][i] = i;
  }

  for (let j = 0; j <= str2.length; j++) {
    matrix[j][0] = j;
  }

  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1, // deletion
        matrix[j - 1][i] + 1, // insertion
        matrix[j - 1][i - 1] + indicator // substitution
      );
    }
  }

  return matrix[str2.length][str1.length];
};

/**
 * Calculate similarity score between two strings (0-1, where 1 is identical)
 */
export const calculateSimilarity = (str1: string, str2: string): number => {
  const maxLength = Math.max(str1.length, str2.length);
  if (maxLength === 0) return 1;
  
  const distance = levenshteinDistance(str1, str2);
  return (maxLength - distance) / maxLength;
};

/**
 * Check if a search term matches a target string using fuzzy matching
 * @param searchTerm - The search term
 * @param target - The target string to search in
 * @param threshold - Similarity threshold (0-1, default 0.6)
 * @returns boolean indicating if there's a match
 */
export const fuzzyMatch = (searchTerm: string, target: string, threshold: number = 0.6): boolean => {
  if (!searchTerm || !target) return true;
  
  const normalizedSearch = removeAccents(searchTerm.trim());
  const normalizedTarget = removeAccents(target.trim());
  
  // Exact match (after normalization)
  if (normalizedTarget.includes(normalizedSearch)) {
    return true;
  }
  
  // Fuzzy match using similarity score
  const similarity = calculateSimilarity(normalizedSearch, normalizedTarget);
  return similarity >= threshold;
};

/**
 * Check if search term matches any of the target strings
 * @param searchTerm - The search term
 * @param targets - Array of target strings to search in
 * @param threshold - Similarity threshold (0-1, default 0.6)
 * @returns boolean indicating if there's a match in any target
 */
export const fuzzyMatchAny = (searchTerm: string, targets: string[], threshold: number = 0.6): boolean => {
  if (!searchTerm) return true;
  
  return targets.some(target => fuzzyMatch(searchTerm, target, threshold));
};

/**
 * Enhanced product search function
 * @param products - Array of products to search
 * @param searchTerm - The search term
 * @param threshold - Similarity threshold for fuzzy matching (default 0.6)
 * @returns Filtered array of products
 */
export const searchProducts = <T extends {
  name: string;
  brand: string;
  ean: string;
  external_id?: string;
}>(
  products: T[],
  searchTerm: string,
  threshold: number = 0.6
): T[] => {
  if (!searchTerm.trim()) return products;
  
  return products.filter(product => {
    const searchFields = [
      product.name,
      product.brand,
      product.ean,
      product.external_id || ''
    ];
    
    return fuzzyMatchAny(searchTerm, searchFields, threshold);
  });
};

/**
 * Custom hook for debounced search
 * @param callback - Function to call after debounce
 * @param delay - Delay in milliseconds (default 500ms)
 * @returns Debounced function
 */
export const useDebounce = <T extends (...args: any[]) => void>(
  callback: T,
  delay: number = 500
): T => {
  const timeoutRef = useRef<NodeJS.Timeout>();

  return useCallback(
    ((...args: Parameters<T>) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      timeoutRef.current = setTimeout(() => {
        callback(...args);
      }, delay);
    }) as T,
    [callback, delay]
  );
};

/**
 * Custom hook for debounced search term state
 * @param initialValue - Initial search term value
 * @param delay - Debounce delay in milliseconds (default 500ms)
 * @returns [debouncedValue, setValue, immediateValue]
 */
export const useDebouncedSearch = (
  initialValue: string = '',
  delay: number = 500
): [string, (value: string) => void, string] => {
  const [immediateValue, setImmediateValue] = useState(initialValue);
  const [debouncedValue, setDebouncedValue] = useState(initialValue);

  const debouncedSetValue = useDebounce((value: string) => {
    setDebouncedValue(value);
  }, delay);

  const setValue = useCallback((value: string) => {
    setImmediateValue(value);
    debouncedSetValue(value);
  }, [debouncedSetValue]);

  return [debouncedValue, setValue, immediateValue];
};

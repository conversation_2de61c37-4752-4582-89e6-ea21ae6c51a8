import React, { useState, useEffect } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { productService } from "@/services/api";
import { GetActiveProductsResponse } from "@/types/api";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Package, Search, Plus, Edit, Trash2, Image as ImageIcon, RefreshCw, CheckCircle2, AlertTriangle, Filter, X } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { PaginationWithWindow } from "@/components/ui/pagination";
import { useScreenSize } from "@/hooks/use-mobile";
import { isProductByWeight } from "@/utils/formatters";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, DialogContent, DialogHeader, Di<PERSON>Title, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/components/ui/use-toast";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { useData } from "@/contexts/DataContext";


interface ProductFormData {
  name: string;
  brand: string;
  ean: string;
  categories: string[];
  is_18_plus: boolean;
  image?: File;
}

const ITEMS_PER_PAGE = 10;

const Products = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { products, categories, isLoading: isDataLoading, refreshProducts } = useData();
  const screenSize = useScreenSize();

  // All state hooks must be called before any early returns
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [showReviewedOnly, setShowReviewedOnly] = useState<null | boolean>(null);
  const [showActiveOnly, setShowActiveOnly] = useState<null | boolean>(null);
  const [show18PlusOnly, setShow18PlusOnly] = useState<null | boolean>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<GetActiveProductsResponse | null>(null);
  const [formData, setFormData] = useState<ProductFormData>({
    name: "",
    brand: "",
    ean: "",
    categories: [],
    is_18_plus: false,
  });

  // All mutation hooks must be called before any early returns
  // Create product mutation
  const createProduct = useMutation({
    mutationFn: async (data: FormData) => {
      const response = await productService.createProduct(data);
      return response.data;
    },
    onSuccess: () => {
      refreshProducts(); // Refresh after creating product
      setIsCreateDialogOpen(false);
      setFormData({
        name: "",
        brand: "",
        ean: "",
        categories: [],
        is_18_plus: false,
        image: undefined,
      });
      toast({
        title: "Success",
        description: "Product created successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to create product",
      });
    },
  });

  // Update product mutation
  const updateProduct = useMutation({
    mutationFn: async ({ externalId, data }: { externalId: string; data: any }) => {
      const response = await productService.updateProduct(externalId, data);
      return response.data;
    },
    onSuccess: () => {
      refreshProducts(); // Refresh after updating product
      setIsEditDialogOpen(false);
      setFormData({
        name: "",
        brand: "",
        ean: "",
        categories: [],
        is_18_plus: false,
        image: undefined,
      });
      toast({
        title: "Success",
        description: "Product updated successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to update product",
      });
    },
  });

  // Update product image mutation
  const updateProductImage = useMutation({
    mutationFn: async ({ externalId, image }: { externalId: string; image: File }) => {
      const formData = new FormData();
      formData.append("image", image);
      const response = await productService.updateProductImage(externalId, formData);
      return response.data;
    },
    onSuccess: () => {
      refreshProducts(); // Refresh after updating product image
      toast({
        title: "Success",
        description: "Product image updated successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to update product image",
      });
    },
  });

  // Update product active status mutation
  const updateProductActiveStatus = useMutation({
    mutationFn: async ({ externalId, isActive }: { externalId: string; isActive: boolean }) => {
      const response = await productService.updateProduct(externalId, { is_active: isActive });
      return response.data;
    },
    onSuccess: () => {
      refreshProducts(); // Refresh after updating product status
      toast({
        title: "Success",
        description: "Product status updated successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to update product status",
      });
    },
  });

  // Show loading while data is being loaded - after all hooks
  if (isDataLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Carregando produtos...</p>
        </div>
      </div>
    );
  }

  // Filter products based on search term and toggles
  const filteredProducts = products.filter((product) => {
    const matchesSearch = 
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.brand.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.ean.includes(searchTerm) ||
      product.external_id.includes(searchTerm);

    // Each switch now toggles between true/false for the property
    const matchesReviewed = showReviewedOnly === null ? true : product.is_reviewed === showReviewedOnly;
    const matchesActive = showActiveOnly === null ? true : product.is_active === showActiveOnly;
    const matches18Plus = show18PlusOnly === null ? true : product.is_18_plus === show18PlusOnly;

    return matchesSearch && matchesReviewed && matchesActive && matches18Plus;
  });

  // Pagination
  const totalPages = Math.ceil(filteredProducts.length / ITEMS_PER_PAGE);
  const paginatedProducts = filteredProducts.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );



  const handleCreateProduct = async (e: React.FormEvent) => {
    e.preventDefault();
    const formDataObj = new FormData();
    const { name, brand, ean, categories, is_18_plus, image } = formData;
    formDataObj.append("name", name);
    formDataObj.append("brand", brand);
    formDataObj.append("ean", ean);
    const categoryObjects = categories.map(catStr => JSON.parse(catStr));
    formDataObj.append("categories", JSON.stringify(categoryObjects));
    formDataObj.append("is_18_plus", is_18_plus.toString());
    if (image) {
      formDataObj.append("image", image);
    }
    createProduct.mutate(formDataObj);
  };

  const handleUpdateProduct = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedProduct) return;
    const { name, brand, ean, categories: selectedCategoryIds, is_18_plus } = formData;
    
    // Format categories according to the expected struct
    const formattedCategories = selectedCategoryIds.map(categoryId => {
      const category = categories.find(c => c.external_id === categoryId);
      if (!category) return null;
      return {
        name: category.name,
        image: category.image,
        external_id: category.external_id
      };
    }).filter(Boolean);
    
    // Prepare JSON payload
    const payload = {
      name,
      brand,
      ean,
      is_18_plus,
      is_reviewed: true,
      is_active: true,
      categories: formattedCategories
    };
    
    updateProduct.mutate({ externalId: selectedProduct.external_id, data: payload });
  };

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>, externalId: string) => {
    const file = e.target.files?.[0];
    if (!file) return;
    updateProductImage.mutate({ externalId, image: file });
  };

  const handleEditClick = (product: GetActiveProductsResponse) => {
    setSelectedProduct(product);
    setFormData({
      name: product.name,
      brand: product.brand,
      ean: product.ean,
      categories: product.categories?.map(c => c.external_id) || [],
      is_18_plus: product.is_18_plus,
    });
    setIsEditDialogOpen(true);
  };

  // Add a helper to reset all filters
  const resetFilters = () => {
    setShowReviewedOnly(null);
    setShowActiveOnly(null);
    setShow18PlusOnly(null);
    setSearchTerm("");
  };



  return (
    <div className="space-y-4 sm:space-y-6">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
        <div className="space-y-1">
          <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold">Catálogo de Produtos</h1>
          <p className="text-sm sm:text-base text-muted-foreground">
            Gerencie os produtos disponíveis
          </p>
        </div>
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-4">
          <Button
            variant="outline"
            onClick={() => refreshProducts()}
            className="w-full sm:w-auto"
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            <span className="hidden sm:inline">Atualizar</span>
            <span className="sm:hidden">Atualizar Lista</span>
          </Button>
          <Dialog open={isCreateDialogOpen} onOpenChange={(open) => {
            setIsCreateDialogOpen(open);
            if (!open) {
              setFormData({
                name: "",
                brand: "",
                ean: "",
                categories: [],
                is_18_plus: false,
                image: undefined,
              });
            }
          }}>
            <DialogTrigger asChild>
              <Button className="w-full sm:w-auto">
                <Plus className="mr-2 h-4 w-4" />
                <span className="hidden sm:inline">Novo Produto</span>
                <span className="sm:hidden">Criar Produto</span>
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Criar Novo Produto</DialogTitle>
              </DialogHeader>
              <form onSubmit={handleCreateProduct} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Nome</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="brand">Marca</Label>
                  <Input
                    id="brand"
                    value={formData.brand}
                    onChange={(e) => setFormData({ ...formData, brand: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="ean">EAN</Label>
                  <Input
                    id="ean"
                    value={formData.ean}
                    onChange={(e) => setFormData({ ...formData, ean: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="categories">Categorias</Label>
                  <Select
                    value={formData.categories[0]}
                    onValueChange={(value) => setFormData({ ...formData, categories: [value] })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione uma categoria" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.external_id} value={JSON.stringify(category)}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="image">Imagem</Label>
                  <Input
                    id="image"
                    type="file"
                    accept="image/*"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) setFormData({ ...formData, image: file });
                    }}
                    required
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="is_18_plus"
                    checked={formData.is_18_plus}
                    onChange={(e) => setFormData({ ...formData, is_18_plus: e.target.checked })}
                  />
                  <Label htmlFor="is_18_plus">Produto 18+</Label>
                </div>
                <Button type="submit" className="w-full" disabled={createProduct.isPending}>
                  {createProduct.isPending ? "Criando..." : "Criar Produto"}
                </Button>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Filters section */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <CardTitle className="text-lg sm:text-xl flex items-center">
              <Filter className="mr-2 h-4 w-4" />
              Filtros
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={resetFilters}
              className="w-full sm:w-auto"
            >
              <X className="mr-2 h-3 w-3" />
              Limpar Filtros
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search */}
          <div className="flex items-center space-x-2">
            <Search size={16} className="text-gray-400 flex-shrink-0" />
            <Input
              placeholder="Buscar por nome, marca ou EAN..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1"
            />
          </div>

          {/* Filter toggles */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-gray-700">Status de Revisão</h4>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Switch
                    id="show-reviewed"
                    checked={showReviewedOnly === true}
                    onCheckedChange={(checked) => setShowReviewedOnly(checked ? true : false)}
                  />
                  <Label htmlFor="show-reviewed" className="text-sm">Apenas Revisados</Label>
                </div>
                <div className="flex items-center gap-2">
                  <Switch
                    id="show-reviewed-false"
                    checked={showReviewedOnly === false}
                    onCheckedChange={(checked) => setShowReviewedOnly(checked ? false : null)}
                  />
                  <Label htmlFor="show-reviewed-false" className="text-sm">Apenas Não Revisados</Label>
                </div>
              </div>
            </div>
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-gray-700">Status de Ativação</h4>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Switch
                    id="show-active"
                    checked={showActiveOnly === true}
                    onCheckedChange={(checked) => setShowActiveOnly(checked ? true : false)}
                  />
                  <Label htmlFor="show-active" className="text-sm">Apenas Ativos</Label>
                </div>
                <div className="flex items-center gap-2">
                  <Switch
                    id="show-active-false"
                    checked={showActiveOnly === false}
                    onCheckedChange={(checked) => setShowActiveOnly(checked ? false : null)}
                  />
                  <Label htmlFor="show-active-false" className="text-sm">Apenas Inativos</Label>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <h4 className="text-sm font-medium text-gray-700">Classificação Etária</h4>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Switch
                    id="show-18-plus"
                    checked={show18PlusOnly === true}
                    onCheckedChange={(checked) => setShow18PlusOnly(checked ? true : false)}
                  />
                  <Label htmlFor="show-18-plus" className="text-sm">Apenas 18+</Label>
                </div>
                <div className="flex items-center gap-2">
                  <Switch
                    id="show-18-plus-false"
                    checked={show18PlusOnly === false}
                    onCheckedChange={(checked) => setShow18PlusOnly(checked ? false : null)}
                  />
                  <Label htmlFor="show-18-plus-false" className="text-sm">Apenas Não 18+</Label>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-4 sm:p-6">
          {filteredProducts.length === 0 ? (
            <div className="text-center py-12">
              <Package className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-4 text-lg sm:text-xl font-semibold">Nenhum produto encontrado</h3>
              <p className="mt-2 text-sm sm:text-base text-muted-foreground max-w-md mx-auto">
                {searchTerm
                  ? "Nenhum produto corresponde à sua busca. Tente ajustar os filtros."
                  : "Nenhum produto foi criado ainda. Clique em 'Novo Produto' para começar."}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
              {paginatedProducts.map((product) => (
                <Card
                  key={product.external_id}
                  className={`overflow-hidden hover:shadow-lg transition-all duration-200 ${
                    !product.is_active ? "opacity-50" : ""
                  }`}
                >
                  <div className="relative">
                    <div className="h-40 sm:h-48 overflow-hidden bg-gray-100 relative group">
                      <img
                        src={product.image}
                        alt={product.name}
                        className="w-full h-full object-contain transition-transform group-hover:scale-105"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = "https://placehold.co/400x300?text=No+Image";
                        }}
                      />
                      {product.is_active && (
                        <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                          <input
                            type="file"
                            id={`image-${product.external_id}`}
                            className="hidden"
                            accept="image/*"
                            onChange={(e) => handleImageUpload(e, product.external_id)}
                          />
                          <label
                            htmlFor={`image-${product.external_id}`}
                            className="cursor-pointer p-2 bg-white rounded-full hover:bg-gray-100"
                          >
                            <ImageIcon className="h-6 w-6" />
                          </label>
                        </div>
                      )}
                    </div>
                    <div className="absolute top-2 right-2 flex flex-col gap-2">
                      {product.is_reviewed && (
                        <Badge className="bg-emerald-600 hover:bg-emerald-700 text-white font-semibold rounded-full text-sm px-2 py-1 flex items-center gap-1">
                          <CheckCircle2 className="h-3 w-3" />
                          Revisado
                        </Badge>
                      )}
                      {product.is_18_plus && (
                        <Badge className="bg-amber-600 hover:bg-amber-700 text-white font-semibold rounded-full text-sm px-2 py-1 flex items-center gap-1">
                          <AlertTriangle className="h-3 w-3" />
                          18+
                        </Badge>
                      )}
                      {!product.is_active && (
                        <Badge className="bg-gray-600 hover:bg-gray-700 text-white font-semibold rounded-full text-sm px-2 py-1 flex items-center gap-1">
                          <Trash2 className="h-3 w-3" />
                          Inativo
                        </Badge>
                      )}
                    </div>
                  </div>
                  <div className="p-3 sm:p-4 space-y-3">
                    <div>
                      <h3 className="font-medium text-base sm:text-lg line-clamp-2 min-h-[2.5rem]" title={product.name}>
                        {product.name}
                      </h3>
                      <div className="mt-2 space-y-1">
                        <p className="text-xs sm:text-sm text-gray-600 truncate" title={product.brand}>
                          <span className="font-medium">Marca:</span> {product.brand}
                        </p>
                        <p className="text-xs sm:text-sm text-gray-600 font-mono" title={product.ean}>
                          <span className="font-medium">EAN:</span> {product.ean}
                        </p>
                      </div>
                    </div>

                    {product.categories && product.categories.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {product.categories.slice(0, screenSize === 'mobile' ? 2 : 3).map((category, index) => (
                          <Badge
                            key={category.external_id || index}
                            variant="secondary"
                            className="text-xs"
                          >
                            {category.name}
                          </Badge>
                        ))}
                        {product.categories.length > (screenSize === 'mobile' ? 2 : 3) && (
                          <Badge variant="outline" className="text-xs">
                            +{product.categories.length - (screenSize === 'mobile' ? 2 : 3)}
                          </Badge>
                        )}
                      </div>
                    )}

                    {/* Tag para produtos por peso */}
                    {isProductByWeight(product.name) && (
                      <div className="flex justify-start">
                        <Badge
                          variant="outline"
                          className="text-xs bg-orange-50 text-orange-700 border-orange-200"
                        >
                          Vendido por Kg
                        </Badge>
                      </div>
                    )}

                    <div className="flex justify-end gap-1 sm:gap-2 pt-2 border-t">
                      {product.is_active ? (
                        <>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditClick(product)}
                            className="h-8 w-8 sm:h-9 sm:w-auto sm:px-3 touch-manipulation"
                            title="Editar produto"
                          >
                            <Edit className="h-3 w-3 sm:h-4 sm:w-4" />
                            <span className="hidden sm:ml-2 sm:inline">Editar</span>
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 sm:h-9 sm:w-auto sm:px-3 touch-manipulation text-destructive hover:text-destructive"
                                title="Desativar produto"
                              >
                                <Trash2 className="h-3 w-3 sm:h-4 sm:w-4" />
                                <span className="hidden sm:ml-2 sm:inline">Desativar</span>
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Confirmar desativação</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Tem certeza que deseja desativar este produto? Você pode reativá-lo depois.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => {
                                    updateProduct.mutate({
                                      externalId: product.external_id,
                                      data: {
                                        ...product,
                                        is_active: false,
                                        is_reviewed: true
                                      }
                                    });
                                  }}
                                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                >
                                  Desativar
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </>
                      ) : (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            updateProduct.mutate({
                              externalId: product.external_id,
                              data: {
                                ...product,
                                is_active: true,
                                is_reviewed: true
                              }
                            });
                          }}
                          className="w-full"
                        >
                          <CheckCircle2 className="h-4 w-4 mr-2" />
                          Ativar Produto
                        </Button>
                      )}
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}

          {filteredProducts.length > 0 && (
            <div className="flex flex-col items-center gap-4 mt-6">
              <div className="text-sm text-muted-foreground">
                Página {currentPage} de {totalPages} • Total de {filteredProducts.length} produtos
              </div>
              <PaginationWithWindow
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
                windowSize={5}
              />
            </div>
          )}
        </CardContent>
      </Card>

      <Dialog open={isEditDialogOpen} onOpenChange={(open) => {
        setIsEditDialogOpen(open);
        if (!open) setSelectedProduct(null);
      }}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Editar Produto</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleUpdateProduct} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="edit-name">Nome</Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-brand">Marca</Label>
              <Input
                id="edit-brand"
                value={formData.brand}
                onChange={(e) => setFormData({ ...formData, brand: e.target.value })}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-ean">EAN</Label>
              <Input
                id="edit-ean"
                value={formData.ean}
                onChange={(e) => setFormData({ ...formData, ean: e.target.value })}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-categories">Categorias</Label>
              <Select
                value={formData.categories[0]}
                onValueChange={(value) => setFormData({ ...formData, categories: [value] })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione uma categoria" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.external_id} value={category.external_id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="edit-is_18_plus"
                checked={formData.is_18_plus}
                onChange={(e) => setFormData({ ...formData, is_18_plus: e.target.checked })}
              />
              <Label htmlFor="edit-is_18_plus">Produto 18+</Label>
            </div>
            <Button type="submit" className="w-full" disabled={updateProduct.isPending}>
              {updateProduct.isPending ? "Salvando..." : "Salvar Alterações"}
            </Button>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Products;

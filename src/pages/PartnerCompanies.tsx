import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Building, Package, ShoppingBag, Eye } from "lucide-react";
import { Link } from "react-router-dom";
import { usePartnerData } from "@/hooks/usePartnerData";

const PartnerCompanies = () => {
  // Use the custom hook for partner data management
  const { companies: userCompaniesData, metrics, isLoading, debug } = usePartnerData();

  // Debug logging
  React.useEffect(() => {
    console.log('📋 [PartnerCompanies] State Update:', {
      isLoading,
      companiesCount: userCompaniesData?.length || 0,
      metrics,
      debug
    });
  }, [isLoading, userCompaniesData, metrics, debug]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Carregando suas empresas...</p>
        </div>
      </div>
    );
  }



  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Minhas Empresas</h2>
          <p className="text-muted-foreground">
            Empresas que você tem permissão para gerenciar
          </p>
        </div>
      </div>

      {/* Companies Grid */}
      {(Array.isArray(userCompaniesData) && userCompaniesData.length > 0) ? (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {userCompaniesData.map((company) => (
            <Card key={company.external_id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start space-x-3">
                  {company.picture ? (
                    <img
                      src={company.picture}
                      alt={company.name}
                      className="w-16 h-16 rounded-lg object-cover"
                    />
                  ) : (
                    <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                      <Building size={24} className="text-gray-500" />
                    </div>
                  )}
                  <div className="flex-1 min-w-0">
                    <CardTitle className="text-lg truncate">{company.name}</CardTitle>
                    <CardDescription className="text-sm">
                      {company.cnpj || 'CNPJ não informado'}
                    </CardDescription>
                    {company.is_active ? (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 mt-1">
                        Ativa
                      </span>
                    ) : (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 mt-1">
                        Inativa
                      </span>
                    )}
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {/* Company Stats */}
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <Package size={20} className="mx-auto text-blue-600 mb-1" />
                    <p className="text-sm font-medium text-blue-900">
                      {company.products?.length || 0}
                    </p>
                    <p className="text-xs text-blue-600">Produtos</p>
                  </div>
                  <div className={`p-3 rounded-lg ${company.is_active ? 'bg-green-50' : 'bg-red-50'}`}>
                    <ShoppingBag size={20} className={`mx-auto mb-1 ${company.is_active ? 'text-green-600' : 'text-red-600'}`} />
                    <p className={`text-sm font-medium ${company.is_active ? 'text-green-900' : 'text-red-900'}`}>
                      {company.is_active ? 'Ativa' : 'Inativa'}
                    </p>
                    <p className={`text-xs ${company.is_active ? 'text-green-600' : 'text-red-600'}`}>Status</p>
                  </div>
                </div>

                {/* Company Info */}
                <div className="space-y-2 text-sm">
                  {company.email && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Email:</span>
                      <span className="truncate ml-2">{company.email}</span>
                    </div>
                  )}
                  {company.phone_numbers?.[0] && company.phone_numbers.length > 0 && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Telefone:</span>
                      <span className="ml-2">{company.phone_numbers[0]}</span>
                    </div>
                  )}
                  {company.address && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Cidade:</span>
                      <span className="ml-2">{company.address.city}, {company.address.state}</span>
                    </div>
                  )}
                  {company.delivery_modes && company.delivery_modes.length > 0 && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Modalidades:</span>
                      <span className="ml-2 capitalize">{company.delivery_modes.join(', ')}</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Taxa de entrega:</span>
                    <span className="ml-2">Baseada na distância</span>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-2">
                  <Link to={`/partner/companies/${company.external_id}`} className="flex-1">
                    <Button className="w-full" size="sm">
                      <Eye size={16} className="mr-2" />
                      Gerenciar
                    </Button>
                  </Link>
                </div>

                {/* Bio/Description */}
                {company.bio && (
                  <div className="pt-2 border-t">
                    <p className="text-xs text-muted-foreground line-clamp-2">
                      {company.bio}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="text-center py-12">
            <Building size={64} className="mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Nenhuma empresa encontrada
            </h3>
            <p className="text-gray-500 mb-4">
              Você não tem permissão para gerenciar nenhuma empresa no momento.
            </p>
            <p className="text-sm text-gray-400">
              Entre em contato com o administrador para obter acesso às empresas.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Summary Card */}
      {(Array.isArray(userCompaniesData) && userCompaniesData.length > 0) && (
        <Card>
          <CardHeader>
            <CardTitle>Resumo</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
              <div className="p-4 bg-blue-50 rounded-lg">
                <Building size={24} className="mx-auto text-blue-600 mb-2" />
                <p className="text-2xl font-bold text-blue-900">{metrics.totalCompanies}</p>
                <p className="text-sm text-blue-600">Empresas Gerenciadas</p>
              </div>
              <div className="p-4 bg-green-50 rounded-lg">
                <Package size={24} className="mx-auto text-green-600 mb-2" />
                <p className="text-2xl font-bold text-green-900">
                  {metrics.totalProducts}
                </p>
                <p className="text-sm text-green-600">Total de Produtos</p>
              </div>
              <div className="p-4 bg-purple-50 rounded-lg">
                <ShoppingBag size={24} className="mx-auto text-purple-600 mb-2" />
                <p className="text-2xl font-bold text-purple-900">
                  {metrics.activeCompanies}
                </p>
                <p className="text-sm text-purple-600">Empresas Ativas</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default PartnerCompanies;

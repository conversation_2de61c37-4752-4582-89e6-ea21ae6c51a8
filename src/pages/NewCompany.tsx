import React, { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { companyService } from "@/services/api";
import { toast } from "sonner";
import { ArrowLeft, Building, MapPin, Phone, Upload, Loader2 } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";
import axios from "axios";

// Schema de validação do formulário
const formSchema = z.object({
  // Informações básicas da empresa
  name: z.string().min(2, "Nome deve ter pelo menos 2 caracteres"),
  cnpj: z.string().min(14, "CNPJ deve ter 14 dígitos").max(18, "CNPJ inválido"),
  bio: z.string().min(1, "Descrição é obrigatória"),
  pix_key: z.string().min(1, "Chave PIX é obrigatória"),
  phone_numbers: z.string().min(10, "Telefone deve ter pelo menos 10 dígitos"),

  // Endereço
  address_name: z.string().min(1, "Nome do endereço é obrigatório"),
  address_street: z.string().min(3, "Rua deve ter pelo menos 3 caracteres"),
  address_number: z.string().min(1, "Número é obrigatório"),
  address_neighborhood: z.string().min(2, "Bairro é obrigatório"),
  address_city: z.string().min(2, "Cidade é obrigatória"),
  address_state: z.string().min(2, "Estado é obrigatório"),
  address_zip_code: z.string().min(8, "CEP deve ter pelo menos 8 caracteres"),
  address_complement: z.string().optional(),

  // Localização
  address_location_latitude: z.string()
    .refine(val => !isNaN(parseFloat(val)), "Latitude deve ser um número válido"),
  address_location_longitude: z.string()
    .refine(val => !isNaN(parseFloat(val)), "Longitude deve ser um número válido"),

  // Novos campos
  commission_rate: z.string()
    .refine(val => !isNaN(Number(val)), "Comissão deve ser um número")
    .refine(val => Number(val) >= 10, "Comissão deve ser de pelo menos 10%"),

  // Modalidades de entrega
  delivery_modes: z.array(z.string()).min(1, "Selecione pelo menos uma modalidade de entrega"),
});

type FormValues = z.infer<typeof formSchema>;

const NewCompany = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isSearchingCep, setIsSearchingCep] = useState(false);
  const [isSearchingCoords, setIsSearchingCoords] = useState(false);
  const mapboxToken = "pk.eyJ1IjoiaXp5bWVyY2FkbyIsImEiOiJjbWFvbzFyMG4wN2VyMm1xMmVsbzNtNWwzIn0.nfXQ8xOG6-YMbRxxyIR-Ag";

  // Inicializa formulário com react-hook-form e zod
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      cnpj: "",
      bio: "",
      pix_key: "",
      phone_numbers: "",
      address_name: "",
      address_street: "",
      address_number: "",
      address_complement: "",
      address_neighborhood: "",
      address_city: "",
      address_state: "",
      address_zip_code: "",
      address_location_latitude: "",
      address_location_longitude: "",
      commission_rate: "",
      delivery_modes: ["pickup"], // Retirada vem preenchida por default
    },
  });

  // Monitora mudanças no CEP e faz a busca automática
  const cepValue = form.watch("address_zip_code");
  const numberValue = form.watch("address_number");
  const streetValue = form.watch("address_street");
  const cityValue = form.watch("address_city");
  const stateValue = form.watch("address_state");

  // Função para buscar endereço por CEP automaticamente
  useEffect(() => {
    const cepWithoutMask = cepValue.replace(/\D/g, "");

    // Só busca quando o CEP tiver exatamente 8 dígitos
    if (cepWithoutMask.length === 8 && !isSearchingCep) {
      searchAddressByCep();
    }
  }, [cepValue]);

  // Efeito para buscar coordenadas quando o número for preenchido
  // e já tivermos os dados de rua, cidade e estado
  useEffect(() => {
    if (
      numberValue &&
      streetValue &&
      cityValue &&
      stateValue &&
      !isSearchingCoords
    ) {
      searchCoordinates();
    }
  }, [numberValue, streetValue, cityValue, stateValue]);

  // Função para buscar endereço por CEP
  const searchAddressByCep = async () => {
    const cep = form.getValues("address_zip_code").replace(/\D/g, "");

    if (cep.length !== 8) {
      return;
    }

    setIsSearchingCep(true);

    try {
      const response = await axios.get(`https://viacep.com.br/ws/${cep}/json/`);
      const data = response.data;

      if (data.erro) {
        toast("CEP não encontrado.");
        return;
      }

      form.setValue("address_street", data.logradouro);
      form.setValue("address_neighborhood", data.bairro);
      form.setValue("address_city", data.localidade);
      form.setValue("address_state", data.uf);
      form.setValue("address_complement", data.complemento || "");

      toast("Endereço encontrado com sucesso!");
    } catch (error) {
      toast("Erro ao buscar CEP. Verifique se o CEP está correto.");
      console.error("Erro ao buscar CEP:", error);
    } finally {
      setIsSearchingCep(false);
    }
  };

  // Função para buscar coordenadas geográficas usando Mapbox
  const searchCoordinates = async () => {
    const street = form.getValues("address_street");
    const number = form.getValues("address_number");
    const city = form.getValues("address_city");
    const state = form.getValues("address_state");

    if (!street || !number || !city || !state) {
      return;
    }

    setIsSearchingCoords(true);

    try {
      // Formato da query para o Mapbox Geocoding API
      const searchQuery = `${street}, ${number}, ${city}, ${state}, Brasil`;
      const encodedQuery = encodeURIComponent(searchQuery);

      const response = await axios.get(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodedQuery}.json?access_token=${mapboxToken}&country=br&limit=1`
      );

      if (response.data.features && response.data.features.length > 0) {
        const [longitude, latitude] = response.data.features[0].center;

        form.setValue("address_location_latitude", latitude.toString());
        form.setValue("address_location_longitude", longitude.toString());

        toast("Coordenadas encontradas com sucesso!");
      } else {
        toast("Não foi possível encontrar as coordenadas para este endereço.");
      }
    } catch (error) {
      toast("Erro ao buscar coordenadas geográficas.");
      console.error("Erro ao buscar coordenadas:", error);
    } finally {
      setIsSearchingCoords(false);
    }
  };

  // Manipulador de upload de arquivos
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (file) {
      // Verificar tamanho do arquivo (20MB = 20 * 1024 * 1024 bytes)
      const maxSize = 20 * 1024 * 1024; // 20MB

      if (file.size > maxSize) {
        toast("O arquivo é muito grande. O tamanho máximo permitido é 20MB.");
        return;
      }

      setSelectedFile(file);

      // Cria uma URL para preview da imagem
      const objectUrl = URL.createObjectURL(file);
      setImagePreview(objectUrl);

      // Limpa a URL do preview quando o componente for desmontado
      return () => URL.revokeObjectURL(objectUrl);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  // Mutation para criar empresa
  const createCompanyMutation = useMutation({
    mutationFn: async (data: FormValues) => {
      console.log("Mutation started with data:", data);
      const formData = new FormData();

      // Adicionando os campos obrigatórios conforme schema da API
      formData.append("name", data.name);
      formData.append("cnpj", data.cnpj.replace(/\D/g, "")); // Remove mask
      formData.append("bio", data.bio);
      formData.append("pix_key", data.pix_key.replace(/\D/g, "")); // Remove mask

      // Formatando o telefone para adicionar o prefixo +55 se necessário
      const formattedPhone = data.phone_numbers.startsWith("+55")
        ? data.phone_numbers
        : `+55${data.phone_numbers}`;
      formData.append("phone_numbers", formattedPhone);

      // Campos de endereço
      formData.append("address_name", data.address_name);
      formData.append("address_street", data.address_street);
      formData.append("address_number", data.address_number);
      formData.append("address_neighborhood", data.address_neighborhood);
      formData.append("address_city", data.address_city);
      formData.append("address_state", data.address_state);
      formData.append("address_zip_code", data.address_zip_code);

      // Campo opcional de complemento
      if (data.address_complement) {
        formData.append("address_complement", data.address_complement);
      }

      // Campos de latitude e longitude
      formData.append("address_location_latitude", data.address_location_latitude);
      formData.append("address_location_longitude", data.address_location_longitude);

      // Shipping fee - set to 0 for now (will be managed via shipping rates)
      formData.append("shipping_fee", "0");
      formData.append("commission_rate", (Number(data.commission_rate) * 100).toString());
      formData.append("cashback_rate", "200");

      // Modalidades de entrega
      console.log('delivery_modes:', data.delivery_modes);

      if (data.delivery_modes && data.delivery_modes.length > 0) {
        data.delivery_modes.forEach(mode => {
          formData.append("delivery_modes[]", mode);
        });
      }


      // Imagem da empresa (opcional)
      if (selectedFile) {
        formData.append("image", selectedFile);
      }

      console.log("FormData contents:");
      for (let [key, value] of formData.entries()) {
        console.log(key, value);
      }

      console.log("Calling API...");
      return companyService.createCompany(formData);
    },
    onSuccess: (response) => {
      console.log("API Success response:", response);
      // Invalidate the companies query to force a refetch when navigating back
      queryClient.invalidateQueries({ queryKey: ["companies"] });
      toast("Empresa criada com sucesso!", {
        dismissible: true,
      });

      // Navigate to the company details page
      navigate(`/admin/companies/${response.data.data}`);
    },
    onError: (error: any) => {
      console.log("API Error:", error);
      console.log("Error response:", error.response);
      const errorMsg = error.response?.data?.message || "Erro ao criar empresa";
      toast(errorMsg, {
        dismissible: true,
      });
    },
  });

  // Handler para submissão do formulário
  const onSubmit = (data: FormValues) => {
    console.log("Form submitted with data:", data);
    console.log("Form validation state:", form.formState);
    console.log("Form errors:", form.formState.errors);
    createCompanyMutation.mutate(data);
  };

  // Função de teste para verificar se o botão funciona
  const testSubmit = () => {
    console.log("Test button clicked!");
    console.log("Form values:", form.getValues());
    console.log("Form errors:", form.formState.errors);
    console.log("Form is valid:", form.formState.isValid);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <Button variant="ghost" size="icon" onClick={() => navigate("/admin/companies")}>
          <ArrowLeft size={20} />
        </Button>
        <h2 className="text-2xl font-bold tracking-tight">Nova Empresa Parceira</h2>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Building className="mr-2" size={20} />
            Cadastro de Empresa
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              {/* Informações Básicas */}
              <div>
                <h3 className="text-lg font-medium mb-4">Informações Básicas</h3>
                <div className="grid gap-4 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nome da Empresa *</FormLabel>
                        <FormControl>
                          <Input placeholder="Nome da empresa" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="cnpj"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>CNPJ *</FormLabel>
                        <FormControl>
                          <Input placeholder="00.000.000/0000-00" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="phone_numbers"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Telefone *</FormLabel>
                        <FormControl>
                          <Input placeholder="(00) 00000-0000" {...field} />
                        </FormControl>
                        <FormMessage />
                        <FormDescription>
                          O prefixo +55 será adicionado automaticamente se não for informado
                        </FormDescription>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="pix_key"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Chave PIX *</FormLabel>
                        <FormControl>
                          <Input placeholder="Chave PIX" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="mt-4">
                  <FormItem>
                    <FormLabel>Foto/Logo</FormLabel>
                    <div className="flex flex-col gap-3">
                      <input
                        type="file"
                        accept="image/*"
                        ref={fileInputRef}
                        className="hidden"
                        onChange={handleFileChange}
                      />
                      <div
                        className="border-2 border-dashed border-gray-300 rounded-md p-6 cursor-pointer hover:bg-gray-50 transition-colors flex flex-col items-center justify-center"
                        onClick={triggerFileInput}
                      >
                        {imagePreview ? (
                          <div className="flex flex-col items-center gap-2">
                            <img
                              src={imagePreview}
                              alt="Preview"
                              className="max-h-48 max-w-full rounded-md mb-2"
                            />
                            <p className="text-sm text-gray-600">Clique para trocar a imagem</p>
                          </div>
                        ) : (
                          <div className="flex flex-col items-center gap-2">
                            <Upload size={40} className="text-gray-400" />
                            <p className="text-sm text-gray-500 text-center">
                              Clique para fazer upload da sua logo<br />
                              <span className="text-xs">(Tamanho máximo: 20MB)</span>
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                    <FormDescription>
                      Upload da imagem de perfil ou logo da empresa
                    </FormDescription>
                  </FormItem>
                </div>

                <div className="mt-4">
                  <FormField
                    control={form.control}
                    name="bio"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Biografia/Descrição *</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Breve descrição sobre a empresa"
                            className="min-h-24"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <Separator />

              {/* Endereço */}
              <div>
                <h3 className="text-lg font-medium mb-4 flex items-center">
                  <MapPin className="mr-2" size={18} />
                  Endereço
                </h3>
                <div className="grid gap-4 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="address_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nome do Endereço *</FormLabel>
                        <FormControl>
                          <Input placeholder="Ex: Sede, Filial" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="flex gap-2">
                    <FormField
                      control={form.control}
                      name="address_zip_code"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>CEP *</FormLabel>
                          <div className="flex items-center gap-2">
                            <FormControl>
                              <Input
                                placeholder="00000-000"
                                {...field}
                              />
                            </FormControl>
                            {isSearchingCep && (
                              <Loader2 size={18} className="animate-spin text-gray-400" />
                            )}
                          </div>
                          <FormDescription className="text-xs">
                            Digite os 8 dígitos para busca automática
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="address_street"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Rua/Logradouro *</FormLabel>
                        <FormControl>
                          <Input placeholder="Nome da rua" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="address_number"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Número *</FormLabel>
                        <div className="flex items-center gap-2">
                          <FormControl>
                            <Input placeholder="Número" {...field} />
                          </FormControl>
                          {isSearchingCoords && (
                            <Loader2 size={18} className="animate-spin text-gray-400" />
                          )}
                        </div>
                        <FormDescription className="text-xs">
                          Ao informar o número, buscaremos as coordenadas automaticamente
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="address_complement"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Complemento</FormLabel>
                        <FormControl>
                          <Input placeholder="Apto, sala, etc." {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="address_neighborhood"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Bairro *</FormLabel>
                        <FormControl>
                          <Input placeholder="Bairro" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="address_city"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Cidade *</FormLabel>
                        <FormControl>
                          <Input placeholder="Cidade" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="address_state"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Estado *</FormLabel>
                        <FormControl>
                          <Input placeholder="UF" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid gap-4 md:grid-cols-2 mt-4">
                  <FormField
                    control={form.control}
                    name="address_location_latitude"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Latitude *</FormLabel>
                        <FormControl>
                          <Input placeholder="-23.550520" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="address_location_longitude"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Longitude *</FormLabel>
                        <FormControl>
                          <Input placeholder="-46.633308" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <Separator />

              {/* Modalidades de Entrega */}
              <div>
                <h3 className="text-lg font-medium mb-4">Modalidades de Entrega</h3>
                <FormField
                  control={form.control}
                  name="delivery_modes"
                  render={() => (
                    <FormItem>
                      <div className="mb-4">
                        <FormLabel className="text-base">Selecione as modalidades disponíveis *</FormLabel>
                        <FormDescription>
                          Escolha como os clientes poderão receber os produtos
                        </FormDescription>
                      </div>
                      <div className="space-y-3">
                        <FormField
                          control={form.control}
                          name="delivery_modes"
                          render={({ field }) => {
                            return (
                              <FormItem
                                key="pickup"
                                className="flex flex-row items-start space-x-3 space-y-0"
                              >
                                <FormControl>
                                  <Checkbox
                                    checked={field.value?.includes("pickup")}
                                    onCheckedChange={(checked) => {
                                      return checked
                                        ? field.onChange([...field.value, "pickup"])
                                        : field.onChange(
                                          field.value?.filter(
                                            (value) => value !== "pickup"
                                          )
                                        )
                                    }}
                                  />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  Retirada no local
                                </FormLabel>
                              </FormItem>
                            )
                          }}
                        />
                        <FormField
                          control={form.control}
                          name="delivery_modes"
                          render={({ field }) => {
                            return (
                              <FormItem
                                key="delivery"
                                className="flex flex-row items-start space-x-3 space-y-0"
                              >
                                <FormControl>
                                  <Checkbox
                                    checked={field.value?.includes("delivery")}
                                    onCheckedChange={(checked) => {
                                      return checked
                                        ? field.onChange([...field.value, "delivery"])
                                        : field.onChange(
                                          field.value?.filter(
                                            (value) => value !== "delivery"
                                          )
                                        )
                                    }}
                                  />
                                </FormControl>
                                <FormLabel className="font-normal">
                                  Entrega
                                </FormLabel>
                              </FormItem>
                            )
                          }}
                        />
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid gap-4 md:grid-cols-2 mt-4">
                {/* Informação sobre taxas de entrega */}
                {form.watch("delivery_modes")?.includes("delivery") && (
                  <div className="md:col-span-2 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h4 className="font-medium text-blue-900 mb-2">Taxas de Entrega</h4>
                    <p className="text-sm text-blue-700">
                      Após criar a empresa, você poderá configurar taxas de entrega baseadas na distância.
                      Isso permite definir preços diferentes para entregas próximas e distantes.
                    </p>
                  </div>
                )}
                <FormField
                  control={form.control}
                  name="commission_rate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Comissão (%) *</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Ex: 15.5"
                          type="number"
                          min={10}
                          step="0.1"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        A comissão deve ser de pelo menos 10%
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="flex justify-end space-x-2">
                <Button type="button" variant="outline" onClick={() => navigate("/admin/companies")}>
                  Cancelar
                </Button>
                <Button type="button" variant="secondary" onClick={testSubmit}>
                  Debug Form
                </Button>
                <Button
                  type="submit"
                  disabled={createCompanyMutation.isPending}
                >
                  {createCompanyMutation.isPending
                    ? "Salvando..."
                    : "Salvar Empresa"}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
};

export default NewCompany;

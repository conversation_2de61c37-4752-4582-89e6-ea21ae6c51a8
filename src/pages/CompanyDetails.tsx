import React, { useState, useRef, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { companyService, productService } from "@/services/api";
import { GetActiveCompanySuccessResponse, GetActiveProductsResponse } from "@/types/api";
import { useAccessControl } from "@/hooks/useAccessControl";
import { useAuth } from "@/contexts/AuthContext";
import OwnerManagement from "@/components/OwnerManagement";
import { toast } from "sonner";
import {
  ArrowLeft,
  Building,
  Check,
  Package,
  Plus,
  Search,
  Loader,
  Loader2,
  Trash2,
  Upload,
  BarChart3,
  Edit2,
  Save,
  X,
  Users,
  Power,
  PowerOff
} from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { useForm } from "react-hook-form";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import CompanyOrdersTab from "@/components/CompanyOrdersTab";
import CompanyBillingTab from "@/components/CompanyBillingTab";
import ShippingRatesManager from "@/components/ShippingRatesManager";
import NotificationSettings from "@/components/NotificationSettings";
import ConnectionStatusIndicator from "@/components/ConnectionStatusIndicator";
import NotificationPanel from "@/components/NotificationPanel";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination";
import { Skeleton } from "@/components/ui/skeleton";
import { Checkbox } from "@/components/ui/checkbox";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { useData } from "@/contexts/DataContext";
import { NumericFormat } from 'react-number-format';
import { CurrencyInput } from "@/components/ui/currency-input";
import axios from "axios";

interface ProductFormValues {
  price: number; // Value in centavos
  discount: number;
  stock: number;
}

interface SelectedProduct extends GetActiveProductsResponse {
  price: number;
  discount: number;
  stock: number;
}

// Componente de formulário isolado para evitar problemas com o FormContext
const ProductForm = ({ onSubmit, isPending }: {
  onSubmit: (values: ProductFormValues) => void;
  isPending: boolean;
}) => {
  const form = useForm<ProductFormValues>();

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="text-sm font-medium block mb-1">Preço (R$)</label>
          <CurrencyInput
            placeholder="R$ 0,00"
            className={`border ${form.formState.errors.price ? "border-red-500" : "border-gray-300"}`}
            onValueChange={(value) => {
              form.setValue("price", value); // Value already in centavos
            }}
          />
        </div>
        
        <div>
          <label className="text-sm font-medium block mb-1">Desconto (%)</label>
          <Input 
            type="number" 
            min="0"
            max="100"
            defaultValue={0}
            placeholder="5"
            {...form.register("discount", { valueAsNumber: true })}
          />
        </div>
        
        <div>
          <label className="text-sm font-medium block mb-1">Estoque</label>
          <Input 
            type="number" 
            min="1"
            placeholder="20"
            // If invalid, change border to red
            className={`border ${form.formState.errors.stock ? "border-red-500" : "border-gray-300"}`}
            {...form.register("stock", { valueAsNumber: true, required: true })}
          />
        </div>
      </div>
      
      <div className="flex justify-end space-x-2">
        <Button 
          type="submit" 
          disabled={isPending}
        >
          {isPending && (
            <Loader size={16} className="mr-2 animate-spin" />
          )}
          Adicionar produto
        </Button>
      </div>
    </form>
  );
};

const CompanyDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { products, categories } = useData();
  const { isAdmin } = useAccessControl();

  // Get notification system from AuthContext
  const { notifications } = useAuth();

  // Estado
  const [productDialogOpen, setProductDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedProduct, setSelectedProduct] = useState<GetActiveProductsResponse | null>(null);
  const [activeTab, setActiveTab] = useState("details");
  const [isUploading, setIsUploading] = useState(false);

  // Product filter states
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [showAddedProducts, setShowAddedProducts] = useState(false);
  const [showAvailableProducts, setShowAvailableProducts] = useState(true);

  // Estados para edição de campos
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [editFormData, setEditFormData] = useState<any>({});
  const [isSearchingCep, setIsSearchingCep] = useState(false);
  const [isSearchingCoords, setIsSearchingCoords] = useState(false);
  const [coordsEditable, setCoordsEditable] = useState(false);
  const [addressFieldsEditable, setAddressFieldsEditable] = useState(true);
  const [deliveryModesError, setDeliveryModesError] = useState(false);
  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null);

  // Token do Mapbox (mesmo do NewCompany.tsx)
  const mapboxToken = "pk.eyJ1IjoiaXp5bWVyY2FkbyIsImEiOiJjbWFvbzFyMG4wN2VyMm1xMmVsbzNtNWwzIn0.nfXQ8xOG6-YMbRxxyIR-Ag";

  // Validação das modalidades de entrega
  const isDeliveryModesValid = useMemo(() => {
    return editFormData.delivery_modes && editFormData.delivery_modes.length > 0;
  }, [editFormData.delivery_modes]);

  // Função para buscar coordenadas geográficas usando Mapbox
  const searchCoordinates = useCallback(async () => {
    const street = editFormData.address?.street;
    const number = editFormData.address?.number;
    const city = editFormData.address?.city;
    const state = editFormData.address?.state;

    if (!street || !number || !city || !state) {
      return;
    }

    setIsSearchingCoords(true);

    try {
      // Formato da query para o Mapbox Geocoding API
      const searchQuery = `${street}, ${number}, ${city}, ${state}, Brasil`;
      const encodedQuery = encodeURIComponent(searchQuery);

      const response = await axios.get(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodedQuery}.json?access_token=${mapboxToken}&country=br&limit=1`
      );

      if (response.data.features && response.data.features.length > 0) {
        const [longitude, latitude] = response.data.features[0].center;

        setEditFormData((prev: any) => ({
          ...prev,
          address_location_latitude: latitude.toString(),
          address_location_longitude: longitude.toString()
        }));

        toast("Coordenadas encontradas com sucesso!");
      } else {
        toast("Não foi possível encontrar as coordenadas para este endereço.");
        setCoordsEditable(true); // Permite edição manual se não encontrar
      }
    } catch (error) {
      toast("Erro ao buscar coordenadas geográficas.");
      console.error("Erro ao buscar coordenadas:", error);
      setCoordsEditable(true); // Permite edição manual se houver erro
    } finally {
      setIsSearchingCoords(false);
    }
  }, [editFormData.address?.street, editFormData.address?.number, editFormData.address?.city, editFormData.address?.state, mapboxToken]);

  // Função com debounce para buscar coordenadas
  const debouncedSearchCoordinates = useCallback(() => {
    // Cancelar timer anterior se existir
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }

    // Criar novo timer
    const newTimer = setTimeout(() => {
      searchCoordinates();
    }, 900); // 900ms de debounce

    setDebounceTimer(newTimer);
  }, [debounceTimer, searchCoordinates]);

  // Monitora mudanças no CEP e faz a busca automática
  useEffect(() => {
    if (!editModalOpen || !editFormData.address?.zip_code) return;

    const cepWithoutMask = editFormData.address.zip_code.replace(/\D/g, "");

    // Só busca quando o CEP tiver exatamente 8 dígitos
    if (cepWithoutMask.length === 8 && !isSearchingCep) {
      searchAddressByCep();
    }
  }, [editFormData.address?.zip_code, editModalOpen]);

  // Efeito para buscar coordenadas quando o número for preenchido
  // e já tivermos os dados de rua, cidade e estado (com debounce)
  useEffect(() => {
    if (!editModalOpen) return;

    const { address } = editFormData;
    if (
      address?.number &&
      address?.street &&
      address?.city &&
      address?.state &&
      !isSearchingCoords
    ) {
      debouncedSearchCoordinates();
    }

    // Cleanup do timer quando o componente for desmontado ou dependências mudarem
    return () => {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
    };
  }, [
    editFormData.address?.number,
    editFormData.address?.street,
    editFormData.address?.city,
    editFormData.address?.state,
    editModalOpen,
    debouncedSearchCoordinates,
    debounceTimer,
    isSearchingCoords
  ]);

  // Consulta de detalhes da empresa (admin route)
  const { data: companyInfo, isLoading: isLoadingCompany } = useQuery({
    queryKey: ["company", id],
    queryFn: async () => {
      if (!id) throw new Error("ID não fornecido");
      const response = await companyService.getCompanyDetails(id);
      return response.data as GetActiveCompanySuccessResponse;
    },
    enabled: !!id,
  });
  const companyData = companyInfo?.data;

  // Verifica se a empresa já tem o produto (moved before filteredProducts)
  const isProductAlreadyAdded = (productId: string) => {
    return companyData?.products?.some((product) => product.external_id === productId) || false;
  };

  // Enhanced product filtering with category and status filters
  const filteredProducts = useMemo(() => {
    return products.filter((product) => {
      // Search term filter
      const matchesSearch = !searchTerm || (
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.brand.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.external_id.includes(searchTerm) ||
        product.ean.includes(searchTerm)
      );

      // Category filter
      const matchesCategory = selectedCategories.length === 0 ||
        product.categories?.some(category =>
          selectedCategories.includes(category.external_id)
        );

      // Product status filter (added vs available)
      const isAdded = isProductAlreadyAdded(product.external_id);
      const matchesStatus = (showAddedProducts && isAdded) ||
                           (showAvailableProducts && !isAdded);

      // Base filters (reviewed and active)
      const isValidProduct = product.is_reviewed && product.is_active;

      return matchesSearch && matchesCategory && matchesStatus && isValidProduct;
    });
  }, [products, searchTerm, selectedCategories, showAddedProducts, showAvailableProducts, companyData?.products]);

  // Mutation para atualizar imagem da empresa
  const updateCompanyImageMutation = useMutation({
    mutationFn: async (file: File) => {
      if (!id) throw new Error("ID da empresa não fornecido");

      const formData = new FormData();
      formData.append('image', file);

      return companyService.updateCompanyImage(id, formData);
    },
    onSuccess: () => {
      toast("Imagem atualizada com sucesso!");
      // Atualiza os dados da empresa
      queryClient.invalidateQueries({ queryKey: ["company", id] });
      setIsUploading(false);
    },
    onError: (error: any) => {
      const errorMsg = error.response?.data?.message || "Erro ao atualizar imagem";
      toast(errorMsg);
      setIsUploading(false);
    },
  });

  // Mutation para atualizar dados da empresa
  const updateCompanyMutation = useMutation({
    mutationFn: async (data: any) => {
      if (!id) throw new Error("ID da empresa não fornecido");
      return companyService.updateCompany(id, data);
    },
    onSuccess: () => {
      toast("Alterações solicitadas estão pendentes de aprovação", {
        dismissible: true,
      });
      // Atualiza os dados da empresa e a lista global de empresas
      queryClient.invalidateQueries({ queryKey: ["company", id] });
      queryClient.invalidateQueries({ queryKey: ["companies"] });
      setEditModalOpen(false);
      setEditFormData({});
    },
    onError: (error: any) => {
      const errorMsg = error.response?.data?.message || "Erro ao atualizar dados";
      toast(errorMsg, {
        dismissible: true,
      });
    },
  });

  // Mutation para atualizar status da empresa
  const updateStatusMutation = useMutation({
    mutationFn: async (activate: boolean) => {
      if (!id) throw new Error("ID da empresa não fornecido");
      return companyService.updateCompanyStatus(id, activate);
    },
    onSuccess: (_, activate) => {
      toast(`Empresa ${activate ? 'ativada' : 'desativada'} com sucesso!`);
      queryClient.invalidateQueries({ queryKey: ["company", id] });
      queryClient.invalidateQueries({ queryKey: ["companies"] });
    },
    onError: (error: any) => {
      toast(error.response?.data?.message || "Erro ao atualizar status da empresa");
    },
  });

  // Mutation para adicionar produtos à empresa
  const addProductsMutation = useMutation({
    mutationFn: ({ productId, price, discount, stock }: {
      productId: string;
      price: number; // Already in centavos from CurrencyInput
      discount: number;
      stock: number
    }) => {
      if (!id) throw new Error("ID da empresa não fornecido");
      // Price is already in centavos from CurrencyInput
      return companyService.addProductToCompany(id, {
        product_external_id: productId,
        price: price,
        discount,
        stock
      });
    },
    onSuccess: () => {
      toast("Produto adicionado com sucesso!");
      // Atualiza os dados da empresa
      queryClient.invalidateQueries({ queryKey: ["company", id] });
      setSelectedProduct(null);
      setProductDialogOpen(false);
    },
    onError: (error: any) => {
      const errorMsg = error.response?.data?.message || "Erro ao adicionar produto";
      toast(errorMsg);
    },
  });

  // Mutation para remover produtos da empresa
  const removeProductMutation = useMutation({
    mutationFn: (productId: string) => {
      if (!id) throw new Error("ID da empresa não fornecido");
      return companyService.removeProductsFromCompany(id, {
        product_external_id: productId,
        price: 0,
        discount: 0,
        stock: 0
      });
    },
    onSuccess: () => {
      toast("Produto removido com sucesso!");
      // Atualiza os dados da empresa
      queryClient.invalidateQueries({ queryKey: ["company", id] });
    },
    onError: (error: any) => {
      const errorMsg = error.response?.data?.message || "Erro ao remover produto";
      toast(errorMsg);
    },
  });

  // Handler para selecionar produto
  const handleProductSelection = (product: GetActiveProductsResponse) => {
    if (isProductAlreadyAdded(product.external_id)) {
      toast("Este produto já foi adicionado");
      return;
    }
    
    setSelectedProduct(product);
  };
  
  // Handler para adicionar produto com preço, desconto e estoque
  const handleAddProduct = (values: ProductFormValues) => {
    if (!selectedProduct) return;
    
    addProductsMutation.mutate({
      productId: selectedProduct.external_id,
      price: values.price || 0,
      discount: values.discount || 0,
      stock: values.stock || 0
    });
  };

  // Handler para remover produto
  const handleRemoveProduct = (productId: string) => {
    if (confirm("Tem certeza que deseja remover este produto?")) {
      removeProductMutation.mutate(productId);
    }
  };
  
  // Função para fechar o modal e limpar seleção
  const handleCloseModal = () => {
    setProductDialogOpen(false);
    setSelectedProduct(null);
    setSearchTerm("");
    setSelectedCategories([]);
    setShowAddedProducts(false);
    setShowAvailableProducts(true);
  };

  // Função para formatar preço
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(price / 100);
  };

  // Função para formatar CNPJ
  const formatCNPJ = (cnpj: string) => {
    if (!cnpj) return '';
    const cleanCNPJ = cnpj.replace(/\D/g, '');
    if (cleanCNPJ.length !== 14) return cnpj;
    return cleanCNPJ.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
  };

  // Funções para edição de campos
  const openEditModal = () => {
    // Get the first address from addresses array or fallback to legacy address field
    const primaryAddress = companyData.addresses && companyData.addresses.length > 0
      ? companyData.addresses[0]
      : companyData.address;

    setEditFormData({
      name: companyData.name || '',
      cnpj: companyData.cnpj || companyData.document || '',
      phone_numbers: companyData.phone_numbers?.[0] || '',
      bio: companyData.bio || '',
      address: primaryAddress || {
        street: '',
        number: '',
        complement: '',
        neighborhood: '',
        city: '',
        state: '',
        zip_code: ''
      },
      address_location_latitude: primaryAddress?.location?.latitude?.toString() || '',
      address_location_longitude: primaryAddress?.location?.longitude?.toString() || '',
      delivery_modes: companyData.delivery_modes || []
    });
    setCoordsEditable(true); // Reset coordenadas para editáveis
    setAddressFieldsEditable(true); // Reset campos de endereço para editáveis
    setDeliveryModesError(false); // Reset erro de modalidades
    setEditModalOpen(true);
  };

  const closeEditModal = () => {
    setEditModalOpen(false);
    setEditFormData({});
  };

  // Função para alternar status da empresa
  const handleToggleStatus = () => {
    if (!companyData) return;

    const newStatus = !companyData.is_active;
    const action = newStatus ? 'ativar' : 'desativar';

    if (window.confirm(`Tem certeza que deseja ${action} esta empresa?`)) {
      updateStatusMutation.mutate(newStatus);
    }
  };

  const saveCompanyData = () => {
    // Validar modalidades de entrega
    if (!editFormData.delivery_modes || editFormData.delivery_modes.length === 0) {
      setDeliveryModesError(true);
      toast("Selecione pelo menos uma modalidade de entrega");
      return;
    }

    setDeliveryModesError(false);

    // Preservar coordenadas existentes se não foram alteradas ou estão vazias
    const getCoordinateValue = (formValue: string, originalValue: number | undefined) => {
      // Se o campo do formulário tem um valor válido, usar ele
      if (formValue && formValue.trim() !== '') {
        const parsed = parseFloat(formValue);
        return !isNaN(parsed) ? parsed : originalValue || null;
      }
      // Se o campo está vazio, preservar o valor original
      return originalValue || null;
    };

    const payload: any = {
      bio: editFormData.bio,
      phone_numbers: [editFormData.phone_numbers],
      address: {
        ...editFormData.address,
        location: {
          latitude: getCoordinateValue(
            editFormData.address_location_latitude,
            (companyData.addresses && companyData.addresses.length > 0
              ? companyData.addresses[0].location?.latitude
              : companyData.address?.location?.latitude)
          ),
          longitude: getCoordinateValue(
            editFormData.address_location_longitude,
            (companyData.addresses && companyData.addresses.length > 0
              ? companyData.addresses[0].location?.longitude
              : companyData.address?.location?.longitude)
          )
        }
      },
      shipping_fee: 0, // Shipping fees are now managed via shipping rates
      delivery_modes: editFormData.delivery_modes
    };

    updateCompanyMutation.mutate(payload);
  };

  // Função para buscar endereço por CEP (reutilizada do NewCompany.tsx)
  const searchAddressByCep = useCallback(async () => {
    const cep = editFormData.address?.zip_code?.replace(/\D/g, "");

    if (!cep || cep.length !== 8) {
      return;
    }

    setIsSearchingCep(true);

    // Limpar campos de endereço antes de nova busca
    setEditFormData(prev => ({
      ...prev,
      address: {
        ...prev.address,
        street: '',
        neighborhood: '',
        city: '',
        state: '',
        complement: ''
      },
      address_location_latitude: '',
      address_location_longitude: ''
    }));

    try {
      const response = await axios.get(`https://viacep.com.br/ws/${cep}/json/`);
      const data = response.data;

      if (data.erro) {
        toast("CEP não encontrado.");
        setAddressFieldsEditable(true); // Permitir edição manual se CEP não encontrado
        return;
      }

      setEditFormData(prev => ({
        ...prev,
        address: {
          ...prev.address,
          street: data.logradouro,
          neighborhood: data.bairro,
          city: data.localidade,
          state: data.uf,
          complement: data.complemento || ''
        }
      }));

      setAddressFieldsEditable(true); // Tornar campos editáveis após preenchimento automático
      toast("Endereço encontrado com sucesso!");
    } catch (error) {
      toast("Erro ao buscar CEP. Verifique se o CEP está correto.");
      console.error("Erro ao buscar CEP:", error);
      setAddressFieldsEditable(true); // Permitir edição manual em caso de erro
    } finally {
      setIsSearchingCep(false);
    }
  }, [editFormData.address?.zip_code]);



  // Handler para upload de imagem
  const handleImageClick = () => {
    fileInputRef.current?.click();
  };

  // Handler para seleção de arquivo
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    // Validar tamanho (max 20MB)
    const maxSizeInBytes = 20 * 1024 * 1024; // 20MB
    if (file.size > maxSizeInBytes) {
      toast("A imagem deve ter no máximo 20MB");
      return;
    }

    // Validar tipo
    if (!file.type.startsWith('image/')) {
      toast("O arquivo selecionado não é uma imagem válida");
      return;
    }
    
    // Upload da imagem
    setIsUploading(true);
    updateCompanyImageMutation.mutate(file);
  };

  if (isLoadingCompany) {
    return <div className="text-center py-8">Carregando detalhes da empresa...</div>;
  }

  if (!companyData) {
    return <div className="text-center py-8 text-red-500">Empresa não encontrada</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="icon" onClick={() => navigate("/admin/companies")}>
            <ArrowLeft size={20} />
          </Button>
          <h2 className="text-2xl font-bold tracking-tight">{companyData.name}</h2>
          <Badge variant={companyData.is_active ? "default" : "secondary"}>
            {companyData.is_active ? "ATIVO" : "INATIVO"}
          </Badge>
        </div>

        <div className="flex items-center gap-3">
          {/* Notification components - only for partner users */}
          {!isAdmin && (
            <>
              {/* Connection Status */}
              <ConnectionStatusIndicator
                status={notifications.connectionState}
                onReconnect={notifications.reconnect}
                showDetails
                variant="icon"
              />

              {/* Notification Panel */}
              <NotificationPanel
                notifications={notifications.notifications}
                unreadCount={notifications.unreadCount}
                onMarkAsRead={notifications.markAsRead}
                onMarkAllAsRead={notifications.markAllAsRead}
                onClearAll={notifications.clearAllNotifications}
              />

              {/* Notification Settings */}
              <NotificationSettings />
            </>
          )}

          {/* Botão de ativar/desativar - apenas para admins */}
          {isAdmin && (
            <Button
              variant={companyData.is_active ? "destructive" : "default"}
              onClick={handleToggleStatus}
              disabled={updateStatusMutation.isPending}
              className="flex items-center space-x-2"
            >
              {updateStatusMutation.isPending ? (
                <Loader size={16} className="animate-spin" />
              ) : companyData.is_active ? (
                <PowerOff size={16} />
              ) : (
                <Power size={16} />
              )}
              <span>
                {updateStatusMutation.isPending
                  ? "Processando..."
                  : companyData.is_active
                    ? "Desativar"
                    : "Ativar"
                }
              </span>
            </Button>
          )}
        </div>
      </div>

      {/* Imagem da empresa com upload */}
      <div className="w-full flex justify-center mb-6">
        <div 
          className="relative w-32 h-32 sm:w-40 sm:h-40 rounded-full overflow-hidden border-4 border-white shadow-lg cursor-pointer group"
          onClick={handleImageClick}
        >
          {companyData.picture ? (
            <img
              src={companyData.picture}
              alt={`Logo da ${companyData.name}`}
              className="w-full h-full object-cover transition-opacity group-hover:opacity-75"
              onError={(e) => {
                (e.target as HTMLImageElement).src = "https://placehold.co/400x400?text=Sem+Imagem";
              }}
            />
          ) : (
            <div className="w-full h-full bg-gray-200 flex items-center justify-center text-gray-500 transition-colors group-hover:bg-gray-300">
              Sem imagem
            </div>
          )}
          
          {/* Overlay de upload */}
          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity">
            {isUploading ? (
              <Loader size={24} className="text-white animate-spin" />
            ) : (
              <div className="flex flex-col items-center">
                <Upload size={24} className="text-white mb-1" />
                <span className="text-xs text-white font-medium">Alterar imagem</span>
              </div>
            )}
          </div>
          
          {/* Input escondido para seleção do arquivo */}
          <input 
            type="file"
            ref={fileInputRef}
            className="hidden"
            accept="image/*"
            onChange={handleFileChange}
            disabled={isUploading}
          />
        </div>
      </div>

      {/* Tabs para detalhes e proprietário (admin) ou detalhes e pedidos (partner) */}
      <Tabs
        defaultValue="details"
        value={activeTab}
        onValueChange={setActiveTab}
        className="w-full"
      >
        <TabsList className={`grid w-full ${isAdmin ? 'grid-cols-4' : 'grid-cols-4'}`}>
          <TabsTrigger value="details">Detalhes</TabsTrigger>
          {!isAdmin && <TabsTrigger value="orders">Pedidos</TabsTrigger>}
          <TabsTrigger value="shipping">Taxas de Entrega</TabsTrigger>
          <TabsTrigger value="billing">Faturamento</TabsTrigger>
          {isAdmin && (
            <TabsTrigger value="owner">Proprietário</TabsTrigger>
          )}
        </TabsList>

        {/* Conteúdo da Tab de Detalhes */}
        <TabsContent value="details" className="space-y-6 mt-6">
          {/* Detalhes da empresa */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="flex items-center">
                <Building className="mr-2" size={20} />
                Detalhes do Parceiro
              </CardTitle>
              <Button onClick={openEditModal}>
                <Edit2 size={16} className="mr-2" />
                Editar Dados
              </Button>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <p className="text-sm font-medium text-gray-500">Nome</p>
                  <p className="text-lg">{companyData.name}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">CNPJ</p>
                  <p className="text-lg">{formatCNPJ(companyData.cnpj || companyData.document)}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Telefone</p>
                  <p className="text-lg">{companyData.phone_numbers?.[0]}</p>
                </div>
                {/* Bio */}
                <div className="md:col-span-2">
                  <p className="text-sm font-medium text-gray-500">Bio</p>
                  <p className="text-lg">{companyData.bio || 'Nenhuma descrição cadastrada'}</p>
                </div>
                {companyData.pix_key && (
                  <div>
                    <p className="text-sm font-medium text-gray-500">Chave PIX</p>
                    <p className="text-lg">{companyData.pix_key}</p>
                  </div>
                )}
                {companyData.status && (
                  <div>
                    <p className="text-sm font-medium text-gray-500">Status</p>
                    <Badge variant={companyData.status === "active" ? "default" : "secondary"}>
                      {companyData.status}
                    </Badge>
                  </div>
                )}
                {/* Endereço */}
                <div className="md:col-span-2">
                  <p className="text-sm font-medium text-gray-500">Endereço</p>
                  <p className="text-lg">
                    {companyData.addresses && companyData.addresses.length > 0 ? (
                      <>
                        {companyData.addresses[0].street}, {companyData.addresses[0].number} {companyData.addresses[0].complement ? ` - ${companyData.addresses[0].complement}` : ''} <br />
                        {companyData.addresses[0].neighborhood}, {companyData.addresses[0].city} - {companyData.addresses[0].state} <br />
                        CEP: {companyData.addresses[0].zip_code}
                      </>
                    ) : companyData.address ? (
                      <>
                        {companyData.address.street}, {companyData.address.number} {companyData.address.complement ? ` - ${companyData.address.complement}` : ''} <br />
                        {companyData.address.neighborhood}, {companyData.address.city} - {companyData.address.state} <br />
                        CEP: {companyData.address.zip_code}
                      </>
                    ) : (
                      'Nenhum endereço cadastrado'
                    )}
                  </p>
                </div>

                {/* Taxa de entrega */}
                <div>
                  <p className="text-sm font-medium text-gray-500">Taxa de Entrega</p>
                  <p className="text-lg">
                    Baseada na distância
                  </p>
                  <p className="text-xs text-gray-400">
                    Configurada na aba "Taxas de Entrega"
                  </p>
                </div>

                {/* Modalidades de entrega */}
                <div>
                  <p className="text-sm font-medium text-gray-500">Modalidades de Entrega</p>
                  <div className="flex flex-wrap gap-2">
                    {companyData.delivery_modes && companyData.delivery_modes.length > 0 ? (
                      companyData.delivery_modes.map((mode) => (
                        <Badge key={mode} variant="secondary">
                          {mode === 'pickup' ? 'Retirada no local' : 'Entrega'}
                        </Badge>
                      ))
                    ) : (
                      <span className="text-lg">Nenhuma modalidade configurada</span>
                    )}
                  </div>
                </div>

                {companyData.created_at && (
                  <div>
                    <p className="text-sm font-medium text-gray-500">Criado em</p>
                    <p className="text-lg">{new Date(companyData.created_at).toLocaleDateString('pt-BR')}</p>
                  </div>
                )}
                {companyData.updated_at && (
                  <div>
                    <p className="text-sm font-medium text-gray-500">Atualizado em</p>
                    <p className="text-lg">{new Date(companyData.updated_at).toLocaleDateString('pt-BR')}</p>
                  </div>
                )}

                {/* Proprietário - apenas para admins */}
                {isAdmin && (
                  <div className="md:col-span-2">
                    <p className="text-sm font-medium text-gray-500">Proprietário</p>
                    {companyData.owner ? (
                      <div className="flex items-center space-x-2">
                        <p className="text-lg">{companyData.owner.name}</p>
                      </div>
                    ) : (
                      <p className="text-lg text-gray-400">Nenhum proprietário vinculado</p>
                    )}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Produtos da empresa */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="flex items-center">
                <Package className="mr-2" size={20} />
                Produtos
              </CardTitle>
              <Button onClick={() => setProductDialogOpen(true)}>
                <Plus className="mr-2" size={18} />
                Adicionar Produto
              </Button>
            </CardHeader>
            <CardContent>
              {companyData.products && companyData.products.length > 0 ? (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Imagem</TableHead>
                        <TableHead>Nome</TableHead>
                        <TableHead>Marca</TableHead>
                        <TableHead>EAN</TableHead>
                        <TableHead>Preço</TableHead>
                        <TableHead>Desconto</TableHead>
                        <TableHead>Estoque</TableHead>
                        <TableHead>Categorias</TableHead>
                        <TableHead>SKU</TableHead>
                        <TableHead>Ações</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {companyData.products.map((product) => (
                        <TableRow key={product.external_id}>
                          <TableCell>
                            <div className="h-12 w-12 overflow-hidden rounded-md">
                              {product.image ? (
                                <img
                                  src={product.image}
                                  alt={product.name}
                                  className="h-full w-full object-cover"
                                  onError={(e) => {
                                    (e.target as HTMLImageElement).src = "https://placehold.co/100x100?text=No+Image";
                                  }}
                                />
                              ) : (
                                <div className="h-full w-full bg-gray-200 flex items-center justify-center text-gray-500">
                                  Sem imagem
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className="font-medium">{product.name}</TableCell>
                          <TableCell>{product.brand}</TableCell>
                          <TableCell>{product.ean}</TableCell>
                          <TableCell>{formatPrice(product.price || 0)}</TableCell>
                          <TableCell>{product.discount || 0}%</TableCell>
                          <TableCell>{product.stock || 0}</TableCell>
                          <TableCell>
                            <div className="flex flex-wrap gap-1">
                              {product.categories?.map((category, index) => (
                                <Badge 
                                  key={category.external_id || index}
                                  variant="secondary"
                                  className="text-xs"
                                >
                                  {category.name}
                                </Badge>
                              ))}
                            </div>
                          </TableCell>
                          <TableCell>{product.external_id}</TableCell>
                          <TableCell>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleRemoveProduct(product.external_id)}
                              className="text-red-500 hover:text-red-700 hover:bg-red-50"
                            >
                              <Trash2 size={16} />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="mb-4">Este parceiro ainda não tem produtos associados.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Conteúdo da Tab de Taxas de Entrega */}
        <TabsContent value="shipping" className="mt-6">
          <ShippingRatesManager
            companyExternalId={companyData?.external_id || ""}
            disabled={false} // Allow both admins and partners to edit shipping rates
          />
        </TabsContent>

        {/* Conteúdo da Tab de Pedidos - Apenas para Partners */}
        {!isAdmin && (
          <TabsContent value="orders" className="mt-6">
            <CompanyOrdersTab
              companyData={companyData}
              notificationState={{
                unreadCount: notifications.unreadCount,
                lastNotificationTime: notifications.lastNotificationTime,
                connectionState: notifications.connectionState,
              }}
            />
          </TabsContent>
        )}

        {/* Conteúdo da Tab de Faturamento */}
        <TabsContent value="billing" className="mt-6">
          <CompanyBillingTab companyId={companyData?.external_id} />
        </TabsContent>



        {/* Conteúdo da Tab de Proprietário - Apenas para Admins */}
        {isAdmin && (
          <TabsContent value="owner" className="mt-6">
            <OwnerManagement
              companyExternalId={id || ""}
              currentOwner={companyData?.owner || null}
            />
          </TabsContent>
        )}
      </Tabs>

      {/* Modal para editar dados da empresa */}
      <Dialog open={editModalOpen} onOpenChange={closeEditModal}>
        <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Editar Dados da Empresa</DialogTitle>
            <DialogDescription>
              Atualize as informações da empresa. Campos marcados com * são obrigatórios.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4 space-y-6">
            {/* Informações básicas */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium block mb-1">Nome da Empresa</label>
                <Input
                  value={editFormData.name || ''}
                  disabled
                  className="bg-gray-50"
                />
              </div>
              <div>
                <label className="text-sm font-medium block mb-1">CNPJ</label>
                <Input
                  value={formatCNPJ(editFormData.cnpj || '')}
                  disabled
                  className="bg-gray-50"
                />
              </div>
              <div>
                <label className="text-sm font-medium block mb-1">Telefone *</label>
                <Input
                  placeholder="(00) 00000-0000"
                  value={editFormData.phone_numbers || ''}
                  onChange={(e) => setEditFormData({ ...editFormData, phone_numbers: e.target.value })}
                />
              </div>
            </div>

            {/* Bio */}
            <div>
              <label className="text-sm font-medium block mb-1">Descrição/Bio *</label>
              <Textarea
                placeholder="Descrição da empresa"
                className="min-h-20"
                value={editFormData.bio || ''}
                onChange={(e) => setEditFormData({ ...editFormData, bio: e.target.value })}
              />
            </div>

            {/* Endereço */}
            <div>
              <h3 className="text-lg font-medium mb-3">Endereço</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium block mb-1">Rua *</label>
                  <Input
                    placeholder="Nome da rua"
                    value={editFormData.address?.street || ''}
                    disabled={!addressFieldsEditable}
                    className={!addressFieldsEditable ? "bg-gray-50" : ""}
                    onChange={(e) => setEditFormData({
                      ...editFormData,
                      address: { ...editFormData.address, street: e.target.value }
                    })}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium block mb-1">Número *</label>
                  <div className="flex items-center gap-2">
                    <Input
                      placeholder="Número"
                      value={editFormData.address?.number || ''}
                      onChange={(e) => setEditFormData({
                        ...editFormData,
                        address: { ...editFormData.address, number: e.target.value }
                      })}
                    />
                    {isSearchingCoords && (
                      <Loader2 size={18} className="animate-spin text-gray-400" />
                    )}
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Ao informar o número, buscaremos as coordenadas automaticamente
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium block mb-1">Complemento</label>
                  <Input
                    placeholder="Apto, sala, etc."
                    value={editFormData.address?.complement || ''}
                    disabled={!addressFieldsEditable}
                    className={!addressFieldsEditable ? "bg-gray-50" : ""}
                    onChange={(e) => setEditFormData({
                      ...editFormData,
                      address: { ...editFormData.address, complement: e.target.value }
                    })}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium block mb-1">Bairro *</label>
                  <Input
                    placeholder="Bairro"
                    value={editFormData.address?.neighborhood || ''}
                    disabled={!addressFieldsEditable}
                    className={!addressFieldsEditable ? "bg-gray-50" : ""}
                    onChange={(e) => setEditFormData({
                      ...editFormData,
                      address: { ...editFormData.address, neighborhood: e.target.value }
                    })}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium block mb-1">Cidade *</label>
                  <Input
                    placeholder="Cidade"
                    value={editFormData.address?.city || ''}
                    disabled={!addressFieldsEditable}
                    className={!addressFieldsEditable ? "bg-gray-50" : ""}
                    onChange={(e) => setEditFormData({
                      ...editFormData,
                      address: { ...editFormData.address, city: e.target.value }
                    })}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium block mb-1">Estado *</label>
                  <Input
                    placeholder="UF"
                    value={editFormData.address?.state || ''}
                    disabled={!addressFieldsEditable}
                    className={!addressFieldsEditable ? "bg-gray-50" : ""}
                    onChange={(e) => setEditFormData({
                      ...editFormData,
                      address: { ...editFormData.address, state: e.target.value }
                    })}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium block mb-1">CEP *</label>
                  <div className="flex items-center gap-2">
                    <Input
                      placeholder="00000-000"
                      value={editFormData.address?.zip_code || ''}
                      onChange={(e) => setEditFormData({
                        ...editFormData,
                        address: { ...editFormData.address, zip_code: e.target.value }
                      })}
                    />
                    {isSearchingCep && (
                      <Loader2 size={18} className="animate-spin text-gray-400" />
                    )}
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Digite os 8 dígitos para busca automática
                  </p>
                </div>
              </div>

              {/* Coordenadas geográficas */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                <div>
                  <label className="text-sm font-medium block mb-1">Latitude</label>
                  <Input
                    placeholder="-23.550520"
                    value={editFormData.address_location_latitude || ''}
                    disabled={!coordsEditable}
                    className={!coordsEditable ? "bg-gray-50" : ""}
                    onChange={(e) => setEditFormData({
                      ...editFormData,
                      address_location_latitude: e.target.value
                    })}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium block mb-1">Longitude</label>
                  <Input
                    placeholder="-46.633308"
                    value={editFormData.address_location_longitude || ''}
                    disabled={!coordsEditable}
                    className={!coordsEditable ? "bg-gray-50" : ""}
                    onChange={(e) => setEditFormData({
                      ...editFormData,
                      address_location_longitude: e.target.value
                    })}
                  />
                </div>
              </div>
            </div>

            {/* Informação sobre taxas de entrega */}
            {editFormData.delivery_modes?.includes('delivery') && (
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <p className="text-sm text-blue-700">
                  <strong>Taxas de Entrega:</strong> Configure taxas baseadas na distância na aba "Taxas de Entrega" para oferecer preços diferenciados por localização.
                </p>
              </div>
            )}

            {/* Modalidades de entrega */}
            <div>
              <label className="text-sm font-medium block mb-3">Modalidades de Entrega *</label>
              <div className={`space-y-2 p-3 rounded-md border ${deliveryModesError ? 'border-red-500 bg-red-50' : 'border-gray-200'}`}>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="edit-pickup"
                    checked={editFormData.delivery_modes?.includes('pickup')}
                    onCheckedChange={(checked) => {
                      const currentModes = editFormData.delivery_modes || [];
                      if (checked) {
                        setEditFormData({
                          ...editFormData,
                          delivery_modes: [...currentModes, 'pickup']
                        });
                      } else {
                        setEditFormData({
                          ...editFormData,
                          delivery_modes: currentModes.filter((mode: string) => mode !== 'pickup')
                        });
                      }
                      setDeliveryModesError(false); // Limpar erro ao selecionar
                    }}
                  />
                  <label htmlFor="edit-pickup" className="text-sm font-medium">
                    Retirada no local
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="edit-delivery"
                    checked={editFormData.delivery_modes?.includes('delivery')}
                    onCheckedChange={(checked) => {
                      const currentModes = editFormData.delivery_modes || [];
                      if (checked) {
                        setEditFormData({
                          ...editFormData,
                          delivery_modes: [...currentModes, 'delivery']
                        });
                      } else {
                        setEditFormData({
                          ...editFormData,
                          delivery_modes: currentModes.filter((mode: string) => mode !== 'delivery')
                        });
                      }
                      setDeliveryModesError(false); // Limpar erro ao selecionar
                    }}
                  />
                  <label htmlFor="edit-delivery" className="text-sm font-medium">
                    Entrega
                  </label>
                </div>
              </div>
              {deliveryModesError && (
                <p className="text-sm text-red-600 mt-1">
                  Selecione pelo menos uma modalidade de entrega
                </p>
              )}
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={closeEditModal}>
              Cancelar
            </Button>
            <Button
              onClick={saveCompanyData}
              disabled={updateCompanyMutation.isPending || !isDeliveryModesValid}
            >
              {updateCompanyMutation.isPending ? (
                <>
                  <Loader size={16} className="mr-2 animate-spin" />
                  Salvando...
                </>
              ) : (
                'Salvar Alterações'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal para adicionar produtos */}
      <Dialog open={productDialogOpen} onOpenChange={handleCloseModal}>
        <DialogContent className="sm:max-w-3xl">
          <DialogHeader>
            <DialogTitle>Adicionar Produto</DialogTitle>
            <DialogDescription>
              {selectedProduct 
                ? "Informe o preço, desconto e estoque para o produto selecionado"
                : "Clique em um produto para selecioná-lo"}
            </DialogDescription>
          </DialogHeader>
          
          <div className="py-4">
            {!selectedProduct ? (
              <>
                <div className="flex items-center space-x-2 mb-4">
                  <Search size={20} className="text-gray-400" />
                  <Input
                    placeholder="Buscar por nome, marca ou código"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="flex-1"
                  />
                </div>

                {/* Filtering Controls */}
                <div className="space-y-4 mb-4">
                  {/* Category Filter */}
                  <div>
                    <Label className="text-sm font-medium mb-2 block">Filtrar por Categoria</Label>
                    <Select
                      value={selectedCategories.length === 0 ? "all" : selectedCategories.join(",")}
                      onValueChange={(value) => {
                        if (value === "all") {
                          setSelectedCategories([]);
                        } else {
                          // For now, handle single selection. Can be enhanced for multi-select later
                          setSelectedCategories([value]);
                        }
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Todas as Categorias" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Todas as Categorias</SelectItem>
                        {categories.map((category) => (
                          <SelectItem key={category.external_id} value={category.external_id}>
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Product Status Filters */}
                  <div>
                    <Label className="text-sm font-medium mb-2 block">Exibir Produtos</Label>
                    <div className="flex items-center space-x-6">
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="show-available"
                          checked={showAvailableProducts}
                          onCheckedChange={setShowAvailableProducts}
                        />
                        <Label htmlFor="show-available" className="text-sm">
                          Disponíveis
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="show-added"
                          checked={showAddedProducts}
                          onCheckedChange={setShowAddedProducts}
                        />
                        <Label htmlFor="show-added" className="text-sm">
                          Já Adicionados
                        </Label>
                      </div>
                    </div>
                  </div>
                </div>

                <Separator className="my-4" />
                
                <ScrollArea className="h-[400px] rounded-md">
                  {filteredProducts.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
                      {filteredProducts.map((product) => {
                        const isAdded = isProductAlreadyAdded(product.external_id);
                        const isSelected = selectedProduct?.external_id === product.external_id;
                        
                        return (
                          <div
                            key={product.external_id}
                            className={`relative rounded-lg border p-4 transition-all ${
                              isAdded 
                                ? "bg-green-50 border-green-200" 
                                : isSelected
                                  ? "bg-blue-50 border-blue-200 ring-2 ring-blue-400"
                                  : "hover:bg-gray-50 hover:border-gray-300 cursor-pointer"
                            }`}
                            onClick={() => {
                              if (!isAdded) {
                                handleProductSelection(product);
                              }
                            }}
                          >
                            <div className="flex items-center">
                              <div className="h-16 w-16 overflow-hidden rounded-md mr-4 flex-shrink-0 bg-gray-100">
                                {product.image ? (
                                  <img
                                    src={product.image}
                                    alt={product.name}
                                    className="h-full w-full object-contain"
                                    onError={(e) => {
                                      (e.target as HTMLImageElement).src = "https://placehold.co/100x100?text=No+Image";
                                    }}
                                  />
                                ) : (
                                  <div className="h-full w-full bg-gray-200 flex items-center justify-center text-gray-500">
                                    Sem imagem
                                  </div>
                                )}
                              </div>
                              <div className="flex-1">
                                <h4 className="font-medium">{product.name}</h4>
                                <div className="text-sm text-gray-500">
                                  <span className="mr-4">Marca: {product.brand}</span>
                                  <span>EAN: {product.ean}</span>
                                </div>
                                <div className="flex flex-wrap gap-1 mt-1">
                                  {product.categories?.map((category, index) => (
                                    <Badge 
                                      key={category.external_id || index}
                                      variant="secondary"
                                      className="text-xs"
                                    >
                                      {category.name}
                                    </Badge>
                                  ))}
                                </div>
                              </div>
                              
                              {isAdded && (
                                <div className="ml-2 text-sm font-medium text-green-600 flex items-center bg-green-100 px-3 py-1 rounded-full">
                                  <Check size={16} className="mr-1" />
                                  Adicionado
                                </div>
                              )}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Package size={48} className="mx-auto text-gray-400 mb-4" />
                      <p className="text-gray-500 mb-2">
                        {searchTerm
                          ? "Nenhum produto encontrado para esta busca"
                          : !showAvailableProducts && !showAddedProducts
                            ? "Selecione pelo menos um filtro de status"
                            : !showAvailableProducts
                              ? "Nenhum produto já adicionado encontrado"
                              : !showAddedProducts
                                ? "Nenhum produto disponível encontrado"
                                : "Nenhum produto encontrado"}
                      </p>
                      <p className="text-sm text-gray-400">
                        Tente ajustar os filtros ou termo de busca
                      </p>
                    </div>
                  )}
                </ScrollArea>
              </>
            ) : (
              <div>
                <div className="border p-4 mb-4 rounded-md bg-gray-50">
                  <div className="flex items-center">
                    <div className="h-16 w-16 overflow-hidden rounded-md mr-4">
                      <img
                        src={selectedProduct.image}
                        alt={selectedProduct.name}
                        className="h-full w-full object-contain"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = "https://placehold.co/100x100?text=No+Image";
                        }}
                      />
                    </div>
                    <div>
                      <h3 className="text-lg font-medium">{selectedProduct.name}</h3>
                      <p className="text-sm text-gray-600">
                        {selectedProduct.brand} | EAN: {selectedProduct.ean}
                      </p>
                    </div>
                  </div>
                </div>
                
                <ProductForm 
                  onSubmit={handleAddProduct} 
                  isPending={addProductsMutation.isPending}
                />
              </div>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={handleCloseModal}>
              Fechar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CompanyDetails;

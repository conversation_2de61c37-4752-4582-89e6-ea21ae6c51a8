import React, { useState, useMemo } from "react";
import { use<PERSON>ara<PERSON>, Navigate } from "react-router-dom";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { companyService } from "@/services/api";
import { useAuth } from "@/contexts/AuthContext";
import { GetActiveCompanySuccessResponse } from "@/types/api";
import { ArrowLeft, Building, Package, ShoppingBag, Plus, Trash2, DollarSign, Truck } from "lucide-react";
import { Link } from "react-router-dom";
import CompanyOrdersTab from "@/components/CompanyOrdersTab";
import CompanyBillingTab from "@/components/CompanyBillingTab";
import ShippingRatesManager from "@/components/ShippingRatesManager";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { CurrencyInput } from "@/components/ui/currency-input";
import { isProductByWeight, convertStockForBackend, convertStockForDisplay } from "@/utils/formatters";
import { Badge } from "@/components/ui/badge";

interface ProductFormValues {
  price: number; // Value in centavos
  discount: number;
  stock: number;
}
import AddProductModal from "@/components/AddProductModal";

const PartnerCompanyDetails = () => {
  const { id } = useParams<{ id: string }>();
  const { userCompanies } = useAuth();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState("products");
  const [productDialogOpen, setProductDialogOpen] = useState(false);

  // Verificar se o usuário tem acesso a esta empresa
  const hasAccess = userCompanies.includes(id || "");

  // Se não tem acesso, redirecionar
  if (!hasAccess) {
    return <Navigate to="/partner/dashboard" replace />;
  }

  // Buscar dados da empresa
  const { data: companyResponse, isLoading, error } = useQuery({
    queryKey: ["company", id],
    queryFn: async () => {
      if (!id) throw new Error("ID da empresa não fornecido");
      const response = await companyService.getCompany(id);
      return response.data as GetActiveCompanySuccessResponse;
    },
    enabled: !!id,
  });



  const companyData = companyResponse?.data;





  // Mutation para remover produtos da empresa
  const removeProductMutation = useMutation({
    mutationFn: (productId: string) => {
      if (!id) throw new Error("ID da empresa não fornecido");
      return companyService.removeProductsFromCompany(id, {
        product_external_id: productId,
        price: 0,
        discount: 0,
        stock: 0
      });
    },
    onSuccess: () => {
      toast("Produto removido com sucesso!");
      queryClient.invalidateQueries({ queryKey: ["company", id] });
    },
    onError: (error: any) => {
      const errorMsg = error.response?.data?.message || "Erro ao remover produto";
      toast(errorMsg);
    },
  });

  // Handler para remover produto
  const handleRemoveProduct = (productId: string) => {
    if (confirm("Tem certeza que deseja remover este produto?")) {
      removeProductMutation.mutate(productId);
    }
  };

  // Função para formatar preço
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(price / 100);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Carregando dados da empresa...</p>
        </div>
      </div>
    );
  }

  if (error || !companyData) {
    return (
      <div className="text-center py-8">
        <p className="text-red-500 mb-4">Erro ao carregar dados da empresa</p>
        <Link to="/partner/dashboard">
          <Button variant="outline">Voltar ao Dashboard</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link to="/partner/dashboard">
            <Button variant="outline" size="icon">
              <ArrowLeft size={16} />
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold">{companyData.name}</h1>
            <p className="text-muted-foreground">Gerenciar empresa</p>
          </div>
        </div>
      </div>

      {/* Company Info Card - Read Only */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Building className="mr-2" size={20} />
            Informações da Empresa
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <div>
              <p className="text-sm font-medium text-gray-500">Nome</p>
              <p className="text-lg">{companyData.name}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">CNPJ</p>
              <p className="text-lg">{companyData.cnpj || companyData.document}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Email</p>
              <p className="text-lg">{companyData.owner?.email}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Telefone</p>
              <p className="text-lg">{companyData.phone_numbers?.[0] || "Não informado"}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Status</p>
              <p className="text-lg capitalize">{companyData.is_active ? "Ativo" : "Inativo"}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Produtos</p>
              <p className="text-lg">{companyData.products?.length || 0}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-500">Taxa de Entrega</p>
              <p className="text-lg">Baseada na distância</p>
              <p className="text-xs text-gray-400">Configure na aba "Taxas de Entrega"</p>
            </div>
          </div>
          {companyData.bio && (
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">Descrição</p>
              <p className="text-base mt-1">{companyData.bio}</p>
            </div>
          )}
          {companyData.address && (
            <div className="mt-4">
              <p className="text-sm font-medium text-gray-500">Endereço</p>
              <p className="text-base mt-1">
                {companyData.address.street}, {companyData.address.number}
                {companyData.address.complement && `, ${companyData.address.complement}`}
                <br />
                {companyData.address.neighborhood}, {companyData.address.city} - {companyData.address.state}
                <br />
                CEP: {companyData.address.zip_code}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Tabs for Management */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="products" className="flex items-center">
            <Package className="mr-2" size={16} />
            Produtos
          </TabsTrigger>
          <TabsTrigger value="orders" className="flex items-center">
            <ShoppingBag className="mr-2" size={16} />
            Pedidos
          </TabsTrigger>
          <TabsTrigger value="shipping" className="flex items-center">
            <Truck className="mr-2" size={16} />
            Taxas de Entrega
          </TabsTrigger>
          <TabsTrigger value="billing" className="flex items-center">
            <DollarSign className="mr-2" size={16} />
            Faturamento
          </TabsTrigger>
        </TabsList>

        {/* Products Tab */}
        <TabsContent value="products" className="mt-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="flex items-center">
                <Package className="mr-2" size={20} />
                Produtos
              </CardTitle>
              <Button onClick={() => setProductDialogOpen(true)}>
                <Plus className="mr-2" size={18} />
                Adicionar Produto
              </Button>
            </CardHeader>
            <CardContent>
              {companyData.products && companyData.products.length > 0 ? (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Imagem</TableHead>
                        <TableHead>Nome</TableHead>
                        <TableHead>Marca</TableHead>
                        <TableHead>EAN</TableHead>
                        <TableHead>Preço</TableHead>
                        <TableHead>Desconto</TableHead>
                        <TableHead>Estoque</TableHead>
                        <TableHead>Categorias</TableHead>
                        <TableHead>SKU</TableHead>
                        <TableHead>Ações</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {companyData.products.map((product) => (
                        <TableRow key={product.external_id}>
                          <TableCell>
                            {product.image ? (
                              <img
                                src={product.image}
                                alt={product.name}
                                className="w-12 h-12 object-cover rounded-lg"
                              />
                            ) : (
                              <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                                <Package size={16} className="text-gray-500" />
                              </div>
                            )}
                          </TableCell>
                          <TableCell className="font-medium">{product.name}</TableCell>
                          <TableCell>{product.brand}</TableCell>
                          <TableCell>{product.ean}</TableCell>
                          <TableCell>{formatPrice(product.price || 0)}</TableCell>
                          <TableCell>{product.discount || 0}%</TableCell>
                          <TableCell>
                            {convertStockForDisplay(product.stock || 0, product.name)}
                            {isProductByWeight(product.name) && (
                              <span className="text-xs text-gray-500 ml-1">kg</span>
                            )}
                          </TableCell>
                          <TableCell>
                            <div className="flex flex-wrap gap-1">
                              {product.categories?.map((category) => (
                                <span
                                  key={category.external_id}
                                  className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800"
                                >
                                  {category.name}
                                </span>
                              )) || "Sem categoria"}
                            </div>
                          </TableCell>
                          <TableCell>{product.external_id}</TableCell>
                          <TableCell>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleRemoveProduct(product.external_id)}
                              className="text-red-500 hover:text-red-700 hover:bg-red-50"
                            >
                              <Trash2 size={16} />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="mb-4">Este parceiro ainda não tem produtos associados.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Orders Tab */}
        <TabsContent value="orders" className="mt-6">
          <CompanyOrdersTab companyData={companyData} />
        </TabsContent>

        {/* Shipping Rates Tab */}
        <TabsContent value="shipping" className="mt-6">
          <ShippingRatesManager
            companyExternalId={companyData?.external_id || ""}
            disabled={false} // Allow both admins and partners to edit shipping rates
          />
        </TabsContent>

        {/* Billing Tab */}
        <TabsContent value="billing" className="mt-6">
          <CompanyBillingTab companyId={companyData?.external_id} />
        </TabsContent>

      </Tabs>

      {/* Modal unificado para adicionar produtos */}
      <AddProductModal
        open={productDialogOpen}
        onOpenChange={setProductDialogOpen}
        companyId={id!}
        companyProducts={companyData?.products || []}
        onProductAdded={() => {
          // Refresh company data after product is added
          queryClient.invalidateQueries({ queryKey: ["partner-company", id] });
        }}
      />
    </div>
  );
};

export default PartnerCompanyDetails;

/**
 * Modal unificado para adicionar produtos à empresa
 * Usado tanto na visão de admin quanto na de parceiro
 */

import React, { useState, useMemo } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Search, Package, Loader, Filter, X } from "lucide-react";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { companyService, productService } from "@/services/api";
import { GetActiveProductsResponse } from "@/types/api";
import { searchProducts, useDebouncedSearch } from "@/utils/searchUtils";
import { CurrencyInput } from "@/components/ui/currency-input";
import { isProductByWeight, convertStockForBackend, convertStockForDisplay } from "@/utils/formatters";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";

interface ProductFormValues {
  price: number; // Value in centavos
  discount: number;
  stock: number;
}

interface AddProductModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  companyId: string;
  companyProducts?: Array<{ external_id: string }>;
  onProductAdded?: () => void;
}

// Componente de formulário isolado para evitar problemas com o FormContext
const ProductForm = ({ onSubmit, isPending, selectedProduct }: {
  onSubmit: (values: ProductFormValues) => void;
  isPending: boolean;
  selectedProduct?: GetActiveProductsResponse;
}) => {
  const form = useForm<ProductFormValues>();

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <div className="flex items-center gap-2 mb-1">
            <label className="text-sm font-medium">Preço (R$)</label>
            {selectedProduct && isProductByWeight(selectedProduct.name) && (
              <Badge variant="outline" className="text-xs bg-orange-50 text-orange-700 border-orange-200">
                Por Kg
              </Badge>
            )}
          </div>
          <CurrencyInput
            placeholder="R$ 0,00"
            className={`border ${form.formState.errors.price ? "border-red-500" : "border-gray-300"}`}
            onValueChange={(value) => {
              form.setValue("price", value); // Value already in centavos
            }}
          />
          {selectedProduct && isProductByWeight(selectedProduct.name) && (
            <p className="text-xs text-orange-600 mt-1">Preço por quilograma</p>
          )}
        </div>

        <div>
          <label className="text-sm font-medium block mb-1">Desconto (%)</label>
          <Input
            type="number"
            min="0"
            max="100"
            defaultValue={0}
            placeholder="5"
            {...form.register("discount", { valueAsNumber: true })}
          />
        </div>

        <div>
          <div className="flex items-center gap-2 mb-1">
            <label className="text-sm font-medium">Estoque</label>
            {selectedProduct && isProductByWeight(selectedProduct.name) && (
              <Badge variant="outline" className="text-xs bg-orange-50 text-orange-700 border-orange-200">
                Em Kg
              </Badge>
            )}
          </div>
          <Input
            type="number"
            min="0"
            step={selectedProduct && isProductByWeight(selectedProduct.name) ? "0.1" : "1"}
            placeholder={selectedProduct && isProductByWeight(selectedProduct.name) ? "0.0" : "20"}
            className={`border ${form.formState.errors.stock ? "border-red-500" : "border-gray-300"}`}
            {...form.register("stock", { valueAsNumber: true, required: true })}
          />
          {selectedProduct && isProductByWeight(selectedProduct.name) && (
            <p className="text-xs text-orange-600 mt-1">Quantidade em quilogramas (ex: 2.5 kg)</p>
          )}
        </div>
      </div>

      <div className="flex justify-end space-x-2">
        <Button
          type="submit"
          disabled={isPending}
        >
          {isPending && (
            <Loader size={16} className="mr-2 animate-spin" />
          )}
          Adicionar produto
        </Button>
      </div>
    </form>
  );
};

const AddProductModal: React.FC<AddProductModalProps> = ({
  open,
  onOpenChange,
  companyId,
  companyProducts = [],
  onProductAdded
}) => {
  const queryClient = useQueryClient();
  const [selectedProduct, setSelectedProduct] = useState<GetActiveProductsResponse | null>(null);
  const [debouncedSearchTerm, setSearchTerm, immediateSearchTerm] = useDebouncedSearch("", 500);

  // Product filter states
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [showAddedProducts, setShowAddedProducts] = useState(false);
  const [showAvailableProducts, setShowAvailableProducts] = useState(true);

  // Buscar todos os produtos disponíveis
  const { data: productsResponse, isLoading: isLoadingProducts } = useQuery({
    queryKey: ["products-for-modal"],
    queryFn: async () => {
      const response = await productService.getProducts(1, 10000);
      return response.data;
    },
    enabled: open, // Only fetch when modal is open
  });

  const allProducts = productsResponse?.data || [];

  // Extract unique categories from products
  const availableCategories = useMemo(() => {
    const categoryMap = new Map();
    allProducts.forEach(product => {
      product.categories?.forEach(category => {
        if (!categoryMap.has(category.external_id)) {
          categoryMap.set(category.external_id, category);
        }
      });
    });
    return Array.from(categoryMap.values());
  }, [allProducts]);

  // Verifica se a empresa já tem o produto
  const isProductAlreadyAdded = (productId: string) => {
    return companyProducts.some((product) => product.external_id === productId);
  };

  // Enhanced product filtering with category and status filters
  const filteredProducts = useMemo(() => {
    // First apply search filter using enhanced search
    let searchFilteredProducts = allProducts;
    if (debouncedSearchTerm) {
      searchFilteredProducts = searchProducts(allProducts, debouncedSearchTerm, 0.6);
    }

    // Then apply other filters
    return searchFilteredProducts.filter((product) => {
      // Category filter
      const matchesCategory = selectedCategories.length === 0 ||
        product.categories?.some(category =>
          selectedCategories.includes(category.external_id)
        );

      // Product status filter (added vs available)
      const isAdded = isProductAlreadyAdded(product.external_id);
      const matchesStatus = (showAddedProducts && isAdded) ||
                           (showAvailableProducts && !isAdded);

      // Base filters (reviewed and active)
      const isValidProduct = product.is_reviewed && product.is_active;

      return matchesCategory && matchesStatus && isValidProduct;
    });
  }, [allProducts, debouncedSearchTerm, selectedCategories, showAddedProducts, showAvailableProducts, companyProducts]);

  // Mutation para adicionar produtos à empresa
  const addProductsMutation = useMutation({
    mutationFn: ({ productId, price, discount, stock }: {
      productId: string;
      price: number; // Already in centavos from CurrencyInput
      discount: number;
      stock: number
    }) => {
      return companyService.addProductToCompany(companyId, {
        product_external_id: productId,
        price: price,
        discount,
        stock
      });
    },
    onSuccess: () => {
      toast("Produto adicionado com sucesso!");
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ["company", companyId] });
      queryClient.invalidateQueries({ queryKey: ["partner-company", companyId] });
      setSelectedProduct(null);
      onOpenChange(false);
      onProductAdded?.();
    },
    onError: (error: any) => {
      const errorMsg = error.response?.data?.message || "Erro ao adicionar produto";
      toast(errorMsg);
    },
  });

  // Handler para selecionar produto
  const handleProductSelection = (product: GetActiveProductsResponse) => {
    if (isProductAlreadyAdded(product.external_id)) {
      toast("Este produto já foi adicionado");
      return;
    }
    setSelectedProduct(product);
  };

  // Handler para adicionar produto com preço, desconto e estoque
  const handleAddProduct = (values: ProductFormValues) => {
    if (!selectedProduct) return;

    // Converter estoque se produto for vendido por peso
    const convertedStock = convertStockForBackend(values.stock || 0, selectedProduct.name);

    addProductsMutation.mutate({
      productId: selectedProduct.external_id,
      price: values.price || 0,
      discount: values.discount || 0,
      stock: convertedStock
    });
  };

  // Função para fechar o modal e limpar seleção
  const handleCloseModal = () => {
    onOpenChange(false);
    setSelectedProduct(null);
    setSearchTerm("");
    setSelectedCategories([]);
    setShowAddedProducts(false);
    setShowAvailableProducts(true);
  };

  // Handler para toggle de categoria
  const handleCategoryToggle = (categoryId: string) => {
    setSelectedCategories(prev => 
      prev.includes(categoryId)
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  return (
    <Dialog open={open} onOpenChange={handleCloseModal}>
      <DialogContent className="sm:max-w-3xl">
        <DialogHeader>
          <DialogTitle>Adicionar Produto</DialogTitle>
          <DialogDescription>
            {selectedProduct 
              ? "Informe o preço, desconto e estoque para o produto selecionado"
              : "Clique em um produto para selecioná-lo"}
          </DialogDescription>
        </DialogHeader>
        
        <div className="py-4">
          {!selectedProduct ? (
            <>
              <div className="flex items-center space-x-2 mb-4">
                <Search size={20} className="text-gray-400" />
                <Input
                  placeholder="Buscar por nome, marca ou código"
                  value={immediateSearchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="flex-1"
                />
              </div>

              {/* Filtering Controls */}
              <div className="space-y-4 mb-4">
                {/* Category Filter */}
                {availableCategories.length > 0 && (
                  <div>
                    <label className="text-sm font-medium mb-2 block">Categorias</label>
                    <div className="flex flex-wrap gap-2">
                      {availableCategories.map((category) => (
                        <div key={category.external_id} className="flex items-center space-x-2">
                          <Checkbox
                            id={`category-${category.external_id}`}
                            checked={selectedCategories.includes(category.external_id)}
                            onCheckedChange={() => handleCategoryToggle(category.external_id)}
                          />
                          <label
                            htmlFor={`category-${category.external_id}`}
                            className="text-sm cursor-pointer"
                          >
                            {category.name}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Product Status Filters */}
                <div>
                  <Label className="text-sm font-medium mb-2 block">Exibir Produtos</Label>
                  <div className="flex items-center space-x-6">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="show-available"
                        checked={showAvailableProducts}
                        onCheckedChange={setShowAvailableProducts}
                      />
                      <Label htmlFor="show-available" className="text-sm">
                        Disponíveis
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="show-added"
                        checked={showAddedProducts}
                        onCheckedChange={setShowAddedProducts}
                      />
                      <Label htmlFor="show-added" className="text-sm">
                        Já Adicionados
                      </Label>
                    </div>
                  </div>
                </div>

                {/* Clear Filters */}
                {(selectedCategories.length > 0 || debouncedSearchTerm) && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSelectedCategories([]);
                      setSearchTerm("");
                    }}
                    className="flex items-center gap-2"
                  >
                    <X size={16} />
                    Limpar filtros
                  </Button>
                )}
              </div>

              <Separator className="my-4" />
              
              <ScrollArea className="h-[400px] rounded-md">
                {isLoadingProducts ? (
                  <div className="flex items-center justify-center h-32">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                      <p>Carregando produtos...</p>
                    </div>
                  </div>
                ) : filteredProducts.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
                    {filteredProducts.map((product) => {
                      const isAdded = isProductAlreadyAdded(product.external_id);
                      const isSelected = selectedProduct?.external_id === product.external_id;
                      
                      return (
                        <div
                          key={product.external_id}
                          className={`border rounded-lg p-4 cursor-pointer transition-all ${
                            isSelected
                              ? "border-blue-500 bg-blue-50"
                              : isAdded
                                ? "border-green-500 bg-green-50"
                                : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                          }`}
                          onClick={() => handleProductSelection(product)}
                        >
                          <div className="flex items-center">
                            <div className="h-16 w-16 overflow-hidden rounded-md mr-4">
                              <img
                                src={product.image}
                                alt={product.name}
                                className="h-full w-full object-contain"
                                onError={(e) => {
                                  (e.target as HTMLImageElement).src = "https://placehold.co/100x100?text=No+Image";
                                }}
                              />
                            </div>
                            <div className="flex-1">
                              <h3 className="text-sm font-medium">{product.name}</h3>
                              <p className="text-xs text-gray-600">
                                {product.brand} | EAN: {product.ean}
                              </p>
                              <div className="flex items-center mt-2 space-x-2">
                                {product.categories?.map((category) => (
                                  <Badge key={category.external_id} variant="secondary" className="text-xs">
                                    {category.name}
                                  </Badge>
                                ))}
                                {isAdded && (
                                  <Badge variant="default" className="text-xs bg-green-600">
                                    Já adicionado
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Package size={48} className="mx-auto text-gray-400 mb-4" />
                    <p className="text-gray-500 mb-2">
                      {debouncedSearchTerm
                        ? "Nenhum produto encontrado para esta busca"
                        : !showAvailableProducts && !showAddedProducts
                          ? "Selecione pelo menos um filtro de status"
                          : !showAvailableProducts
                            ? "Nenhum produto já adicionado encontrado"
                            : !showAddedProducts
                              ? "Nenhum produto disponível encontrado"
                              : "Nenhum produto encontrado"}
                    </p>
                    <p className="text-sm text-gray-400">
                      Tente ajustar os filtros ou termo de busca
                    </p>
                  </div>
                )}
              </ScrollArea>
            </>
          ) : (
            <div>
              <div className="border p-4 mb-4 rounded-md bg-gray-50">
                <div className="flex items-center">
                  <div className="h-16 w-16 overflow-hidden rounded-md mr-4">
                    <img
                      src={selectedProduct.image}
                      alt={selectedProduct.name}
                      className="h-full w-full object-contain"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = "https://placehold.co/100x100?text=No+Image";
                      }}
                    />
                  </div>
                  <div>
                    <h3 className="text-lg font-medium">{selectedProduct.name}</h3>
                    <p className="text-sm text-gray-600">
                      {selectedProduct.brand} | EAN: {selectedProduct.ean}
                    </p>
                  </div>
                </div>
              </div>
              
              <ProductForm
                onSubmit={handleAddProduct}
                isPending={addProductsMutation.isPending}
                selectedProduct={selectedProduct}
              />
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AddProductModal;

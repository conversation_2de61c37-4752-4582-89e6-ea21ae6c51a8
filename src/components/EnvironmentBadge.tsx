import React from 'react';
import { cn } from '@/lib/utils';
import { getEnvironmentInfo, EnvironmentBadgeProps } from '@/utils/environment';

/**
 * Environment badge component that displays the current environment
 * Only shows in development and staging environments
 */
export const EnvironmentBadge: React.FC<EnvironmentBadgeProps> = ({ 
  className,
  size = 'sm'
}) => {
  const envInfo = getEnvironmentInfo();

  // Don't render if environment shouldn't be shown (e.g., production)
  if (!envInfo.show) {
    return null;
  }

  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-1.5 text-sm'
  };

  return (
    <span
      className={cn(
        'inline-flex items-center rounded-full font-medium border',
        envInfo.bgColor,
        envInfo.color,
        sizeClasses[size],
        className
      )}
      title={`Environment: ${envInfo.name}`}
    >
      {envInfo.displayName}
    </span>
  );
};

export default EnvironmentBadge;

import React, { useState } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import {
  LogOut,
  BarChart3,
  Building,
  Package,
  Settings,
  Menu,
  X,
  PanelLeft,
  Tag,
  Layers,
  Users,
  ShoppingBag,
  Smartphone,
} from "lucide-react";
import { useIsMobile, useScreenSize } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";
import ResponsiveTestPanel from "./ResponsiveTestPanel";
import EnvironmentBadge from "./EnvironmentBadge";


interface LayoutProps {
  children: React.ReactNode;
}

export const Layout = ({ children }: LayoutProps) => {
  const { logout, user } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const screenSize = useScreenSize();
  const [sidebarOpen, setSidebarOpen] = useState(!isMobile);


  // Navegação do sidebar
  const navigation = [
    { name: "Dashboard", href: "/admin/dashboard", icon: BarChart3 },
    { name: "Usuários", href: "/admin/users", icon: Users },
    { name: "Empresas", href: "/admin/companies", icon: Building },
    { name: "Produtos", href: "/admin/products", icon: Package },
    { name: "Categorias", href: "/admin/categories", icon: Layers },
    { name: "Cupons", href: "/admin/coupons", icon: Tag },
    { name: "Versão Mobile", href: "/admin/min-mobile-version", icon: Smartphone },
  ];

  // Função para verificar se o link está ativo
  const isActive = (path: string) => {
    return location.pathname === path;
  };

  // Função para realizar logout
  const handleLogout = () => {
    logout();
    navigate("/login");
  };



  return (
    <div className="min-h-screen bg-gray-50 overflow-x-hidden max-w-full">
      {/* Header Mobile */}
      <div className="md:hidden bg-white border-b border-gray-200 py-3 px-4 flex items-center justify-between sticky top-0 z-40">
        <div className="flex items-center space-x-2 min-w-0">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="flex-shrink-0"
          >
            {sidebarOpen ? <X size={20} /> : <Menu size={20} />}
          </Button>
          <h1 className="text-lg sm:text-xl font-semibold text-gray-800 truncate">
            Backoffice Parceiros
          </h1>
          <EnvironmentBadge size="sm" />
        </div>
        {user && (
          <div className="flex items-center space-x-2 flex-shrink-0">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleLogout}
              className="text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <LogOut size={16} />
            </Button>
          </div>
        )}
      </div>

      <div className="flex">
        {/* Sidebar */}
        <aside
          className={cn(
            "bg-white border-r border-gray-200 transition-all duration-300 ease-in-out z-30",
            isMobile
              ? sidebarOpen
                ? "fixed inset-0 w-64 shadow-lg"
                : "hidden"
              : sidebarOpen
                ? "w-64"
                : screenSize === 'tablet' ? "w-20" : "w-16"
          )}
        >
          <div className="flex flex-col h-full">
            {/* Sidebar Header */}
            <div className="p-3 sm:p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                {(!isMobile && sidebarOpen) || isMobile ? (
                  <div className="flex items-center space-x-2">
                    <h2 className="text-lg sm:text-xl font-bold text-gray-800">
                      Backoffice
                    </h2>
                    <EnvironmentBadge size="sm" />
                  </div>
                ) : (
                  <h2 className="text-xl font-bold text-gray-800 sr-only">
                    Backoffice
                  </h2>
                )}
                {isMobile && (
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setSidebarOpen(false)}
                    className="md:hidden h-8 w-8"
                  >
                    <X size={16} />
                  </Button>
                )}
              </div>
              {user && ((!isMobile && sidebarOpen) || isMobile) && (
                <div className="mt-3 sm:mt-4">
                  <p className="text-sm font-medium text-gray-700 truncate">{user.name}</p>
                  <p className="text-xs text-gray-500 truncate">{user.email}</p>
                </div>
              )}
            </div>

            {/* Sidebar Navigation */}
            <nav className="flex-1 px-2 py-4 space-y-1 overflow-y-auto">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  className={cn(
                    "flex items-center px-3 sm:px-4 py-2 sm:py-3 rounded-md text-sm font-medium transition-colors touch-manipulation",
                    isActive(item.href)
                      ? "bg-primary text-primary-foreground"
                      : "text-gray-700 hover:bg-gray-100 active:bg-gray-200",
                    !sidebarOpen && !isMobile && "justify-center px-2",
                    "min-h-[44px]" // Ensure minimum touch target size
                  )}
                  onClick={() => isMobile && setSidebarOpen(false)}
                  title={!sidebarOpen && !isMobile ? item.name : undefined}
                >
                  <item.icon
                    size={screenSize === 'mobile' ? 20 : 18}
                    className={cn(
                      sidebarOpen || isMobile ? "mr-3" : "mx-auto"
                    )}
                  />
                  {(sidebarOpen || isMobile) && (
                    <span className="truncate">{item.name}</span>
                  )}
                </Link>
              ))}

            </nav>

            {/* Sidebar Footer */}
            <div className="p-4 border-t border-gray-200">
              <Button
                variant="ghost"
                className="w-full justify-start"
                onClick={handleLogout}
              >
                <LogOut size={18} className="mr-3" />
                {(!isMobile && sidebarOpen) || isMobile ? "Sair" : ""}
              </Button>
            </div>
          </div>
        </aside>

        <main
          className={cn(
            "flex-1 transition-all duration-300 overflow-x-hidden max-w-full",
            isMobile ? "w-full p-4 max-w-[calc(100vw-2rem)]" : sidebarOpen ? "p-6 max-w-[calc(100vw-16rem)]" : "p-6 w-full max-w-[calc(100vw-4rem)]"
          )}
        >
          {/* Botão para expandir o sidebar (apenas em desktop quando colapsado) */}
          {!isMobile && !sidebarOpen && (
            <Button
              variant="outline"
              size="icon"
              onClick={() => setSidebarOpen(true)}
              className="mb-4"
            >
              <Menu size={18} />
            </Button>
          )}
          
          {/* Overlay para fechar o sidebar em dispositivos móveis */}
          {isMobile && sidebarOpen && (
            <div 
              className="fixed inset-0 bg-black/50 z-20"
              onClick={() => setSidebarOpen(false)}
            />
          )}
          
          <div className="w-full animate-fade-in">
            {children}
          </div>
        </main>
      </div>

      {/* Responsive Test Panel - Only in development */}
      {process.env.NODE_ENV === 'development' && <ResponsiveTestPanel />}
    </div>
  );
};

import React, { useState, useEffect } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { companyService } from "@/services/api";
import { toast } from "sonner";
import { Plus, Edit, Trash2, Save, X, Loader2 } from "lucide-react";
import { CompanyShippingRate, CreateCompanyShippingRateRequest, UpdateCompanyShippingRateRequest } from "@/types/api";

interface ShippingRatesManagerProps {
  companyExternalId: string;
  disabled?: boolean;
}

interface ShippingRateFormData {
  distance_min_km: string;
  distance_max_km: string;
  fee_reais: string; // Changed from fee_centavos to fee_reais
}

const ShippingRatesManager: React.FC<ShippingRatesManagerProps> = ({
  companyExternalId,
  disabled = false
}) => {
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [editingRateId, setEditingRateId] = useState<string | null>(null);
  const [formData, setFormData] = useState<ShippingRateFormData>({
    distance_min_km: "",
    distance_max_km: "",
    fee_reais: ""
  });
  const [conflictWarning, setConflictWarning] = useState<string | null>(null);

  const queryClient = useQueryClient();

  // Query to fetch shipping rates
  const { data: shippingRatesData, isLoading, error } = useQuery({
    queryKey: ["companyShippingRates", companyExternalId],
    queryFn: () => companyService.getCompanyShippingRates(companyExternalId),
    enabled: !!companyExternalId,
  });

  const shippingRates = shippingRatesData?.data?.shipping_rates || [];

  // Real-time conflict validation
  useEffect(() => {
    if ((isAddingNew || editingRateId) && formData.distance_min_km && formData.distance_max_km) {
      const distanceMin = parseFloat(formData.distance_min_km);
      const distanceMax = parseFloat(formData.distance_max_km);

      if (!isNaN(distanceMin) && !isNaN(distanceMax) && distanceMin < distanceMax) {
        const conflictingRates = checkRangeConflict(distanceMin, distanceMax, editingRateId || undefined);

        if (conflictingRates.length > 0) {
          const conflictDetails = conflictingRates.map(rate =>
            `${rate.distance_min_km}km - ${rate.distance_max_km}km`
          ).join(", ");
          setConflictWarning(`⚠️ Conflito com: ${conflictDetails}`);
        } else {
          setConflictWarning(null);
        }
      } else {
        setConflictWarning(null);
      }
    } else {
      setConflictWarning(null);
    }
  }, [formData.distance_min_km, formData.distance_max_km, shippingRates, isAddingNew, editingRateId]);

  // Mutation to create shipping rate
  const createMutation = useMutation({
    mutationFn: (data: CreateCompanyShippingRateRequest) =>
      companyService.createCompanyShippingRate(companyExternalId, data),
    onSuccess: () => {
      toast.success("Taxa de entrega criada com sucesso!");
      queryClient.invalidateQueries({ queryKey: ["companyShippingRates", companyExternalId] });
      resetForm();
    },
    onError: (error: any) => {
      console.error("Error creating shipping rate:", error);

      // Check if it's a conflict error from backend
      const errorMessage = error?.response?.data?.message || error?.message || "";
      if (errorMessage.toLowerCase().includes("overlap") ||
          errorMessage.toLowerCase().includes("conflict") ||
          errorMessage.toLowerCase().includes("sobrepos")) {
        toast.error("Conflito de faixas detectado! Verifique se não há sobreposição com taxas existentes.", {
          duration: 5000,
        });
      } else {
        const errorMsg = error.response?.data?.message || "Erro ao criar taxa de entrega";
        toast.error(errorMsg);
      }
    },
  });

  // Mutation to update shipping rate
  const updateMutation = useMutation({
    mutationFn: ({ rateExternalId, data }: { rateExternalId: string; data: UpdateCompanyShippingRateRequest }) =>
      companyService.updateCompanyShippingRate(companyExternalId, rateExternalId, data),
    onSuccess: () => {
      toast.success("Taxa de entrega atualizada com sucesso!");
      queryClient.invalidateQueries({ queryKey: ["companyShippingRates", companyExternalId] });
      resetForm();
    },
    onError: (error: any) => {
      console.error("Error updating shipping rate:", error);

      // Check if it's a conflict error from backend
      const errorMessage = error?.response?.data?.message || error?.message || "";
      if (errorMessage.toLowerCase().includes("overlap") ||
          errorMessage.toLowerCase().includes("conflict") ||
          errorMessage.toLowerCase().includes("sobrepos")) {
        toast.error("Conflito de faixas detectado! Verifique se não há sobreposição com taxas existentes.", {
          duration: 5000,
        });
      } else {
        const errorMsg = error.response?.data?.message || "Erro ao atualizar taxa de entrega";
        toast.error(errorMsg);
      }
    },
  });

  // Mutation to delete shipping rate
  const deleteMutation = useMutation({
    mutationFn: (rateExternalId: string) =>
      companyService.deleteCompanyShippingRate(companyExternalId, rateExternalId),
    onSuccess: () => {
      toast.success("Taxa de entrega removida com sucesso!");
      queryClient.invalidateQueries({ queryKey: ["companyShippingRates", companyExternalId] });
    },
    onError: (error: any) => {
      const errorMsg = error.response?.data?.message || "Erro ao remover taxa de entrega";
      toast.error(errorMsg);
    },
  });

  const resetForm = () => {
    setFormData({
      distance_min_km: "",
      distance_max_km: "",
      fee_reais: ""
    });
    setIsAddingNew(false);
    setEditingRateId(null);
    setConflictWarning(null);
  };

  const handleEdit = (rate: CompanyShippingRate) => {
    setFormData({
      distance_min_km: rate.distance_min_km.toString(),
      distance_max_km: rate.distance_max_km.toString(),
      fee_reais: (rate.fee_centavos / 100).toString() // Convert centavos to reais
    });
    setEditingRateId(rate.external_id);
    setIsAddingNew(false);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    const distanceMin = parseFloat(formData.distance_min_km);
    const distanceMax = parseFloat(formData.distance_max_km);
    const feeReais = parseFloat(formData.fee_reais);

    if (isNaN(distanceMin) || distanceMin < 0) {
      toast.error("Distância mínima deve ser um número válido maior ou igual a 0 (0 = desde a empresa)");
      return;
    }

    if (isNaN(distanceMax) || distanceMax <= 0) {
      toast.error("Distância máxima deve ser um número válido maior que 0");
      return;
    }

    if (distanceMin >= distanceMax) {
      toast.error("Distância máxima deve ser maior que a distância mínima");
      return;
    }

    if (isNaN(feeReais) || feeReais < 0) {
      toast.error("Taxa deve ser um valor válido maior ou igual a 0 (0 = entrega grátis)");
      return;
    }

    // Check for range conflicts
    const conflictingRates = checkRangeConflict(distanceMin, distanceMax, editingRateId || undefined);
    if (conflictingRates.length > 0) {
      const conflictDetails = conflictingRates.map(rate =>
        `${rate.distance_min_km}km - ${rate.distance_max_km}km`
      ).join(", ");

      toast.error(
        `Conflito detectado! A faixa ${distanceMin}km - ${distanceMax}km sobrepõe com: ${conflictDetails}. Configure faixas sem sobreposição.`,
        {
          duration: 6000, // Show longer for complex message
        }
      );
      return;
    }

    // Convert reais to centavos for backend
    const feeCentavos = Math.round(feeReais * 100);

    const requestData = {
      distance_min_km: distanceMin,
      distance_max_km: distanceMax,
      fee_centavos: feeCentavos
    };

    if (editingRateId) {
      updateMutation.mutate({ rateExternalId: editingRateId, data: requestData });
    } else {
      createMutation.mutate(requestData);
    }
  };

  const handleDelete = (rateExternalId: string) => {
    if (window.confirm("Tem certeza que deseja remover esta taxa de entrega?")) {
      deleteMutation.mutate(rateExternalId);
    }
  };

  const formatCurrency = (centavos: number) => {
    if (centavos === 0) {
      return "Grátis";
    }
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(centavos / 100);
  };

  // Function to check for range conflicts
  const checkRangeConflict = (newMin: number, newMax: number, excludeRateId?: string) => {
    const conflictingRates = shippingRates.filter(rate => {
      // Skip the rate being edited
      if (excludeRateId && rate.external_id === excludeRateId) {
        return false;
      }

      // Check for overlap: new range overlaps with existing range
      const existingMin = rate.distance_min_km;
      const existingMax = rate.distance_max_km;

      // Overlap conditions:
      // 1. New range starts inside existing range
      // 2. New range ends inside existing range
      // 3. New range completely contains existing range
      // 4. Existing range completely contains new range
      return (
        (newMin >= existingMin && newMin < existingMax) || // New starts inside existing
        (newMax > existingMin && newMax <= existingMax) || // New ends inside existing
        (newMin <= existingMin && newMax >= existingMax) || // New contains existing
        (existingMin <= newMin && existingMax >= newMax)    // Existing contains new
      );
    });

    return conflictingRates;
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Taxas de Entrega por Distância</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2">Carregando taxas de entrega...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Taxas de Entrega por Distância</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-red-600">
            Erro ao carregar taxas de entrega. Tente novamente.
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Taxas de Entrega por Distância
          {!disabled && !isAddingNew && !editingRateId && (
            <Button
              onClick={() => setIsAddingNew(true)}
              size="sm"
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Adicionar Taxa
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Add/Edit Form */}
        {(isAddingNew || editingRateId) && !disabled && (
          <Card className="border-dashed">
            <CardContent className="pt-6">
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="distance_min_km">Distância Mínima (km)</Label>
                    <Input
                      id="distance_min_km"
                      type="number"
                      min="0"
                      step="0.1"
                      placeholder="0.0"
                      value={formData.distance_min_km}
                      onChange={(e) => setFormData({ ...formData, distance_min_km: e.target.value })}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="distance_max_km">Distância Máxima (km)</Label>
                    <Input
                      id="distance_max_km"
                      type="number"
                      min="0.1"
                      step="0.1"
                      placeholder="5.0"
                      value={formData.distance_max_km}
                      onChange={(e) => setFormData({ ...formData, distance_max_km: e.target.value })}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="fee_reais">Taxa (R$)</Label>
                    <Input
                      id="fee_reais"
                      type="number"
                      min="0"
                      step="0.01"
                      placeholder="0.00"
                      value={formData.fee_reais}
                      onChange={(e) => setFormData({ ...formData, fee_reais: e.target.value })}
                      required
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Ex: 0 = Grátis, 5.00 = R$ 5,00
                    </p>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button
                    type="submit"
                    disabled={createMutation.isPending || updateMutation.isPending}
                    className="flex items-center gap-2"
                  >
                    {(createMutation.isPending || updateMutation.isPending) ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Save className="h-4 w-4" />
                    )}
                    {editingRateId ? "Atualizar" : "Salvar"}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={resetForm}
                    disabled={createMutation.isPending || updateMutation.isPending}
                  >
                    <X className="h-4 w-4 mr-2" />
                    Cancelar
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        )}

        {/* Conflict Warning */}
        {conflictWarning && (
          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-sm text-yellow-800 font-medium">
              {conflictWarning}
            </p>
            <p className="text-xs text-yellow-600 mt-1">
              Ajuste as distâncias para evitar sobreposição com taxas existentes.
            </p>
          </div>
        )}

        {/* Existing Rates List */}
        {shippingRates.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p>Nenhuma taxa de entrega configurada.</p>
            <p className="text-sm">Adicione taxas baseadas na distância para oferecer entrega.</p>
          </div>
        ) : (
          <div className="space-y-3">
            {shippingRates
              .sort((a, b) => a.distance_min_km - b.distance_min_km)
              .map((rate, index) => (
                <div key={rate.external_id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-4">
                    <Badge variant="outline">
                      {rate.distance_min_km}km - {rate.distance_max_km}km
                    </Badge>
                    <span className={`font-medium ${rate.fee_centavos === 0 ? 'text-green-600' : ''}`}>
                      {formatCurrency(rate.fee_centavos)}
                    </span>
                  </div>
                  {!disabled && (
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleEdit(rate)}
                        disabled={isAddingNew || editingRateId !== null}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDelete(rate.external_id)}
                        disabled={deleteMutation.isPending || isAddingNew || editingRateId !== null}
                      >
                        {deleteMutation.isPending ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Trash2 className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  )}
                </div>
              ))}
          </div>
        )}

        {shippingRates.length > 0 && (
          <>
            <Separator />
            <div className="text-sm text-gray-600">
              <p><strong>💡 Dica:</strong> Configure faixas de distância sem sobreposição para evitar conflitos.</p>
              <p><strong>✅ Exemplo correto:</strong> 0-2km, 2-5km, 5-10km (sem sobreposição)</p>
              <p><strong>❌ Evite:</strong> 0-3km, 2-6km (sobreposição entre 2-3km)</p>
              <p><strong>🚛 Distância 0:</strong> Use 0km como distância mínima para entregas muito próximas.</p>
              <p><strong>🆓 Taxa R$ 0:</strong> Use R$ 0,00 para oferecer entrega grátis em determinadas faixas.</p>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default ShippingRatesManager;

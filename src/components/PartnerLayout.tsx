import React, { useState } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import {
  LogOut,
  BarChart3,
  Building,
  Menu,
  X,
  PanelLeft,
  HelpCircle,
  ShoppingBag,
} from "lucide-react";
import { useIsMobile, useScreenSize } from "@/hooks/use-mobile";
import { cn } from "@/lib/utils";
import SupportModal from "./SupportModal";
import ResponsiveTestPanel from "./ResponsiveTestPanel";
import EnvironmentBadge from "./EnvironmentBadge";

interface PartnerLayoutProps {
  children: React.ReactNode;
}

export const PartnerLayout = ({ children }: PartnerLayoutProps) => {
  const { logout, user } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const screenSize = useScreenSize();
  const [sidebarOpen, setSidebarOpen] = useState(!isMobile);
  const [supportModalOpen, setSupportModalOpen] = useState(false);

  // Partner navigation - restricted menu
  const navigation = [
    { name: "Dashboard", href: "/partner/dashboard", icon: BarChart3 },
    { name: "Minhas Empresas", href: "/partner/companies", icon: Building },
    { name: "Pedidos", href: "/partner/orders", icon: ShoppingBag },
  ];

  // Função para verificar se o link está ativo
  const isActive = (path: string) => {
    return location.pathname === path;
  };

  const handleLogout = async () => {
    await logout();
  };

  return (
    <div className="flex h-screen bg-gray-50 overflow-x-hidden max-w-full">
      {/* Mobile Header */}
      {isMobile && (
        <div className="fixed top-0 left-0 right-0 z-40 bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between">
          <div className="flex items-center space-x-2 min-w-0">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="flex-shrink-0 h-8 w-8"
            >
              {sidebarOpen ? <X size={16} /> : <Menu size={16} />}
            </Button>
            <h1 className="text-lg font-semibold text-gray-800 truncate">
              Painel Parceiro
            </h1>
            <EnvironmentBadge size="sm" />
          </div>
          {user && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleLogout}
              className="text-red-600 hover:text-red-700 hover:bg-red-50 flex-shrink-0"
            >
              <LogOut size={16} />
            </Button>
          )}
        </div>
      )}

      {/* Sidebar */}
      <div
        className={cn(
          "fixed inset-y-0 left-0 z-30 flex flex-col bg-white border-r border-gray-200 transition-all duration-300 shadow-lg",
          isMobile
            ? sidebarOpen
              ? "w-64 translate-x-0"
              : "w-64 -translate-x-full"
            : sidebarOpen
            ? "w-64"
            : screenSize === 'tablet' ? "w-20" : "w-16",
          isMobile && "top-16" // Account for mobile header
        )}
      >
        {/* Header do Sidebar */}
        <div className="flex items-center justify-between p-3 sm:p-4 border-b border-gray-200">
          {(sidebarOpen || isMobile) && (
            <div className="flex items-center space-x-2">
              <h1 className="text-lg sm:text-xl font-bold text-gray-900">
                Painel Parceiro
              </h1>
              <EnvironmentBadge size="sm" />
            </div>
          )}

          {/* Botão para colapsar/expandir (apenas em desktop) */}
          {!isMobile && (
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="h-8 w-8"
            >
              {sidebarOpen ? <X size={16} /> : <PanelLeft size={16} />}
            </Button>
          )}

          {/* Botão para fechar em mobile */}
          {isMobile && (
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setSidebarOpen(false)}
              className="h-8 w-8"
            >
              <X size={16} />
            </Button>
          )}
        </div>

        {/* User Info */}
        {(sidebarOpen || isMobile) && user && (
          <div className="px-4 py-3 border-b border-gray-200">
            <p className="text-sm font-medium text-gray-900 truncate">
              {user.name || user.email}
            </p>
            <p className="text-xs text-gray-500 truncate">Parceiro</p>
          </div>
        )}

        {/* Sidebar Navigation */}
        <nav className="flex-1 px-2 py-4 space-y-1 overflow-y-auto">
          {navigation.map((item) => (
            <Link
              key={item.name}
              to={item.href}
              className={cn(
                "flex items-center px-3 sm:px-4 py-2 sm:py-3 rounded-md text-sm font-medium transition-colors touch-manipulation",
                isActive(item.href)
                  ? "bg-primary text-primary-foreground"
                  : "text-gray-700 hover:bg-gray-100 active:bg-gray-200",
                !sidebarOpen && !isMobile && "justify-center px-2",
                "min-h-[44px]" // Ensure minimum touch target size
              )}
              onClick={() => isMobile && setSidebarOpen(false)}
              title={!sidebarOpen && !isMobile ? item.name : undefined}
            >
              <item.icon
                size={screenSize === 'mobile' ? 20 : 18}
                className={cn(
                  sidebarOpen || isMobile ? "mr-3" : "mx-auto"
                )}
              />
              {(sidebarOpen || isMobile) && (
                <span className="truncate">{item.name}</span>
              )}
            </Link>
          ))}
        </nav>

        {/* Footer do Sidebar */}
        <div className="p-4 border-t border-gray-200">
          <div className="space-y-2">
            {/* Botão de Suporte */}
            <Button
              variant="ghost"
              onClick={() => setSupportModalOpen(true)}
              className={cn(
                "w-full justify-start",
                !sidebarOpen && !isMobile && "justify-center px-2"
              )}
              title={!sidebarOpen && !isMobile ? "Suporte" : undefined}
            >
              <HelpCircle
                size={18}
                className={cn(
                  sidebarOpen || isMobile ? "mr-3" : "mx-auto"
                )}
              />
              {(sidebarOpen || isMobile) && "Suporte"}
            </Button>

            {/* Botão de Logout */}
            <Button
              variant="ghost"
              onClick={handleLogout}
              className={cn(
                "w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50",
                !sidebarOpen && !isMobile && "justify-center px-2"
              )}
              title={!sidebarOpen && !isMobile ? "Sair" : undefined}
            >
              <LogOut
                size={18}
                className={cn(
                  sidebarOpen || isMobile ? "mr-3" : "mx-auto"
                )}
              />
              {(sidebarOpen || isMobile) && "Sair"}
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div
        className={cn(
          "flex-1 flex flex-col transition-all duration-300",
          isMobile ? "w-full pt-16" : sidebarOpen ? "ml-64" : screenSize === 'tablet' ? "ml-20" : "ml-16"
        )}
      >
        <main
          className={cn(
            "flex-1 transition-all duration-300 overflow-x-hidden max-w-full",
            isMobile ? "w-full p-4 max-w-[calc(100vw-2rem)]" : sidebarOpen ? "p-4 sm:p-6 max-w-[calc(100vw-16rem)]" : "p-4 sm:p-6 w-full max-w-[calc(100vw-4rem)]"
          )}
        >
          {/* Botão para expandir o sidebar (apenas em desktop quando colapsado) */}
          {!isMobile && !sidebarOpen && (
            <Button
              variant="outline"
              size="icon"
              onClick={() => setSidebarOpen(true)}
              className="mb-4"
            >
              <Menu size={18} />
            </Button>
          )}
          
          {/* Overlay para fechar o sidebar em dispositivos móveis */}
          {isMobile && sidebarOpen && (
            <div 
              className="fixed inset-0 bg-black/50 z-20"
              onClick={() => setSidebarOpen(false)}
            />
          )}
          
          <div className="w-full animate-fade-in">
            {children}
          </div>
        </main>
      </div>
      
      {/* Support Modal */}
      <SupportModal
        open={supportModalOpen}
        onOpenChange={setSupportModalOpen}
      />

      {/* Responsive Test Panel - Only in development */}
      {process.env.NODE_ENV === 'development' && <ResponsiveTestPanel />}
    </div>
  );
};

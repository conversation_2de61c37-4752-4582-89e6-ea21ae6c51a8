/**
 * Demo component to showcase enhanced search functionality
 * This component demonstrates the fuzzy, accent-insensitive, debounced search
 */

import React from 'react';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Search } from 'lucide-react';
import { useDebouncedSearch, searchProducts } from '@/utils/searchUtils';

// Mock product data for demonstration
const mockProducts = [
  {
    external_id: '1',
    name: 'Café Premium Especial',
    brand: 'Três Corações',
    ean: '1234567890123',
    image: 'cafe.jpg',
    is_reviewed: true,
    is_18_plus: false,
    is_active: true,
    categories: []
  },
  {
    external_id: '2',
    name: 'Açúcar Cristal Orgânico',
    brand: 'União',
    ean: '2345678901234',
    image: 'acucar.jpg',
    is_reviewed: true,
    is_18_plus: false,
    is_active: true,
    categories: []
  },
  {
    external_id: '3',
    name: 'Leite Integral UHT',
    brand: 'Nestlé',
    ean: '3456789012345',
    image: 'leite.jpg',
    is_reviewed: true,
    is_18_plus: false,
    is_active: true,
    categories: []
  },
  {
    external_id: '4',
    name: 'Pão de Açúcar Integral',
    brand: 'Wickbold',
    ean: '4567890123456',
    image: 'pao.jpg',
    is_reviewed: true,
    is_18_plus: false,
    is_active: true,
    categories: []
  },
  {
    external_id: '5',
    name: 'Refrigerante Coca-Cola',
    brand: 'Coca-Cola',
    ean: '5678901234567',
    image: 'coca.jpg',
    is_reviewed: true,
    is_18_plus: false,
    is_active: true,
    categories: []
  }
];

const SearchDemo: React.FC = () => {
  const [debouncedSearchTerm, setSearchTerm, immediateSearchTerm] = useDebouncedSearch('', 500);
  
  // Filter products using enhanced search
  const filteredProducts = React.useMemo(() => {
    if (!debouncedSearchTerm) return mockProducts;
    return searchProducts(mockProducts, debouncedSearchTerm, 0.6);
  }, [debouncedSearchTerm]);

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search size={20} />
            Enhanced Product Search Demo
          </CardTitle>
          <p className="text-sm text-gray-600">
            Try searching with accents, partial words, or even slight misspellings!
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Search Input */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
              <Input
                placeholder="Try: 'cafe', 'acucar', 'nestle', 'pao', 'coca'..."
                value={immediateSearchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Search Info */}
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <span>Immediate: "{immediateSearchTerm}"</span>
              <span>Debounced: "{debouncedSearchTerm}"</span>
              <Badge variant="outline">
                {filteredProducts.length} results
              </Badge>
            </div>

            {/* Results */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {filteredProducts.map((product) => (
                <Card key={product.external_id} className="border border-gray-200">
                  <CardContent className="p-4">
                    <div className="space-y-2">
                      <h3 className="font-medium text-lg">{product.name}</h3>
                      <p className="text-sm text-gray-600">Brand: {product.brand}</p>
                      <p className="text-xs text-gray-500">EAN: {product.ean}</p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {filteredProducts.length === 0 && debouncedSearchTerm && (
              <div className="text-center py-8 text-gray-500">
                <Search size={48} className="mx-auto mb-4 text-gray-300" />
                <p>No products found for "{debouncedSearchTerm}"</p>
                <p className="text-sm mt-2">Try a different search term or check for typos</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Feature Highlights */}
      <Card>
        <CardHeader>
          <CardTitle>Search Features</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium text-green-700">✓ Accent-insensitive</h4>
              <p className="text-sm text-gray-600">
                Search "cafe" to find "Café", "acucar" to find "Açúcar"
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-green-700">✓ Case-insensitive</h4>
              <p className="text-sm text-gray-600">
                "CAFE", "Cafe", or "cafe" all work the same
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-green-700">✓ Fuzzy matching</h4>
              <p className="text-sm text-gray-600">
                Handles slight misspellings and variations
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-green-700">✓ Debounced (500ms)</h4>
              <p className="text-sm text-gray-600">
                Waits for you to stop typing before searching
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-green-700">✓ Multi-field search</h4>
              <p className="text-sm text-gray-600">
                Searches name, brand, and EAN code simultaneously
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-green-700">✓ Real-time results</h4>
              <p className="text-sm text-gray-600">
                Results update as you type (after debounce)
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SearchDemo;
